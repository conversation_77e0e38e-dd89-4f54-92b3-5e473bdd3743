// Diagnostic script for country popup debugging
// Add this to browser console to debug issues

console.log('=== Country Popup Diagnostic ===');

// 1. Check if modal exists
const modal = document.getElementById('countrySelectionModal');
console.log('Modal element found:', !!modal);
if (modal) {
    console.log('Modal HTML:', modal.outerHTML.substring(0, 200) + '...');
}

// 2. Check if JavaScript widgets are loaded
console.log('Odoo publicWidget:', typeof window.odoo?.define);
console.log('jQuery available:', typeof $);

// 3. Test AJAX endpoint
console.log('Testing /shop/get_countries endpoint...');
fetch('/shop/get_countries', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'call',
        params: {},
        id: 1
    })
})
.then(response => {
    console.log('Response status:', response.status);
    return response.json();
})
.then(data => {
    console.log('Countries response:', data);
    if (data.result) {
        console.log('Success:', data.result.success);
        console.log('Countries count:', data.result.countries?.length || 0);
        if (data.result.countries?.length > 0) {
            console.log('Sample countries:', data.result.countries.slice(0, 3));
        }
    }
    if (data.error) {
        console.error('API Error:', data.error);
    }
})
.catch(error => {
    console.error('Fetch error:', error);
});

// 4. Check localStorage
console.log('LocalStorage country_selection_shown:', localStorage.getItem('country_selection_shown'));

// 5. Check if CSS is loaded
const countryGrid = document.querySelector('.country-grid');
if (countryGrid) {
    const styles = window.getComputedStyle(countryGrid);
    console.log('Country grid display:', styles.display);
    console.log('Country grid grid-template-columns:', styles.gridTemplateColumns);
} else {
    console.log('Country grid element not found');
}

// 6. Manual modal trigger test
window.testCountryModal = function() {
    console.log('Testing manual modal trigger...');
    if (typeof $ !== 'undefined' && modal) {
        $(modal).modal('show');
        console.log('Modal show triggered');
    } else {
        console.log('Cannot trigger modal - jQuery or modal not available');
    }
};

console.log('=== Diagnostic Complete ===');
console.log('Run testCountryModal() to manually open the modal');
