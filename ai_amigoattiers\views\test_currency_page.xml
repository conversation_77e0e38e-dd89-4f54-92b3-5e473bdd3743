<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Test Currency Page -->
    <template id="test_currency_page" name="Test Currency Page">
        <t t-call="website.layout">
            <div class="container mt-5">
                <h1>Currency Test Page</h1>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Current Currency Info</h5>
                            </div>
                            <div class="card-body">
                                <p><strong>Website Currency:</strong> <span t-esc="website.currency_id.name"/> (<span t-esc="website.currency_id.symbol"/>)</p>
                                <p><strong>Currency ID:</strong> <span t-esc="website.currency_id.id"/></p>
                                
                                <h6 class="mt-3">Test Prices:</h6>
                                <p>Price 1: <span t-field="1000" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/></p>
                                <p>Price 2: <span t-field="2500" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/></p>
                                <p>Price 3: <span t-field="5000" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Currency Controls</h5>
                            </div>
                            <div class="card-body">
                                <button type="button" class="btn btn-primary mb-2" onclick="openCountryModal()">
                                    Open Country Selection
                                </button>
                                <br/>
                                <button type="button" class="btn btn-secondary mb-2" onclick="testCurrencyAPI()">
                                    Test Currency API
                                </button>
                                <br/>
                                <button type="button" class="btn btn-info mb-2" onclick="checkLocalStorage()">
                                    Check Local Storage
                                </button>
                                <br/>
                                <button type="button" class="btn btn-warning mb-2" onclick="clearCurrencyData()">
                                    Clear Currency Data
                                </button>
                                
                                <div id="testResults" class="mt-3">
                                    <!-- Test results will appear here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Available Currencies</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <t t-foreach="request.env['res.currency'].search([('active', '=', True)])" t-as="currency">
                                        <div class="col-md-3 mb-2">
                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                    t-att-onclick="'setCurrency(\'' + currency.name + '\')'">
                                                <t t-esc="currency.name"/> (<t t-esc="currency.symbol"/>)
                                            </button>
                                        </div>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <script type="text/javascript">
                function openCountryModal() {
                    if (window.CountrySelection) {
                        window.CountrySelection.openModal();
                    } else {
                        alert('Country selection not available');
                    }
                }
                
                function testCurrencyAPI() {
                    fetch('/shop/apply_currency', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            jsonrpc: '2.0',
                            method: 'call',
                            params: {
                                currency_code: 'USD'
                            }
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('testResults').innerHTML = 
                            '&lt;div class="alert alert-info"&gt;&lt;strong&gt;API Test Result:&lt;/strong&gt;&lt;br/&gt;' + 
                            JSON.stringify(data, null, 2) + '&lt;/div&gt;';
                    })
                    .catch(error => {
                        document.getElementById('testResults').innerHTML = 
                            '&lt;div class="alert alert-danger"&gt;&lt;strong&gt;API Error:&lt;/strong&gt;&lt;br/&gt;' + 
                            error.toString() + '&lt;/div&gt;';
                    });
                }
                
                function checkLocalStorage() {
                    var data = {
                        selected_currency: localStorage.getItem('selected_currency'),
                        selected_country: localStorage.getItem('selected_country'),
                        country_selection_shown: localStorage.getItem('country_selection_shown')
                    };
                    
                    document.getElementById('testResults').innerHTML = 
                        '&lt;div class="alert alert-info"&gt;&lt;strong&gt;Local Storage:&lt;/strong&gt;&lt;br/&gt;' + 
                        JSON.stringify(data, null, 2) + '&lt;/div&gt;';
                }
                
                function clearCurrencyData() {
                    localStorage.removeItem('selected_currency');
                    localStorage.removeItem('selected_country');
                    localStorage.removeItem('country_selection_shown');
                    
                    // Clear cookies
                    document.cookie = 'selected_currency=; path=/; max-age=0';
                    document.cookie = 'selected_country=; path=/; max-age=0';
                    
                    document.getElementById('testResults').innerHTML = 
                        '&lt;div class="alert alert-success"&gt;Currency data cleared!&lt;/div&gt;';
                }
                
                function setCurrency(currencyCode) {
                    fetch('/shop/apply_currency', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            jsonrpc: '2.0',
                            method: 'call',
                            params: {
                                currency_code: currencyCode
                            }
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            localStorage.setItem('selected_currency', currencyCode);
                            document.cookie = 'selected_currency=' + currencyCode + '; path=/; max-age=86400';
                            window.location.reload();
                        } else {
                            alert('Failed to set currency: ' + data.error);
                        }
                    })
                    .catch(error => {
                        alert('Error: ' + error.toString());
                    });
                }
            </script>
        </t>
    </template>
</odoo>
