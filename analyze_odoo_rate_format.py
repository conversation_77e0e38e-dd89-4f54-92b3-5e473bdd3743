#!/usr/bin/env python3
"""
Analyze the correct Odoo rate format based on 2010 data
"""

def analyze_odoo_format():
    print("=== ANALYZING ODOO RATE FORMAT FROM 2010 DATA ===")
    print()
    
    # From your 2010 data
    rate_2010_col1 = 0.013076054  # First column
    rate_2010_col2 = 76.47566691  # Second column
    
    print("2010 Original Odoo Rates:")
    print(f"Column 1: {rate_2010_col1}")
    print(f"Column 2: {rate_2010_col2}")
    print()
    
    # Let's figure out what these mean
    # If 1 USD = 76.48 INR (approximate 2010 rate)
    usd_to_inr_2010 = 76.48
    
    print("Analysis:")
    print(f"Expected 2010 rate: 1 USD = {usd_to_inr_2010} INR")
    print()
    
    # Check which column matches
    print("Column 1 analysis:")
    print(f"  {rate_2010_col1} ≈ 1/{usd_to_inr_2010} = {1/usd_to_inr_2010:.9f}")
    print(f"  This suggests: Column 1 = 1/exchange_rate (INR per USD)")
    print()
    
    print("Column 2 analysis:")
    print(f"  {rate_2010_col2} ≈ {usd_to_inr_2010}")
    print(f"  This suggests: Column 2 = exchange_rate (USD per INR)")
    print()
    
    # Current wrong rates
    current_wrong = 1.950664091
    current_should_be = 85.94  # Current USD to INR rate
    
    print("=== CURRENT PROBLEM ===")
    print(f"Current wrong rate: {current_wrong}")
    print(f"Should be (like 2010 format): {1/current_should_be:.9f} or {current_should_be}")
    print()
    
    print("=== CONCLUSION ===")
    print("Based on 2010 data, Odoo expects:")
    print("• Column 1: Small decimal (1/exchange_rate)")
    print("• Column 2: Large number (exchange_rate)")
    print()
    print("For current USD rate (1 USD = 85.94 INR):")
    print(f"• Should store: {1/current_should_be:.9f} (not {current_wrong})")
    print(f"• Or store: {current_should_be} (not {current_wrong})")
    print()
    
    # Test conversion
    inr_amount = 889
    print(f"=== CONVERSION TEST ===")
    print(f"₹{inr_amount} INR to USD:")
    print(f"With wrong rate {current_wrong}: ${inr_amount * current_wrong:.2f} USD (WRONG!)")
    print(f"With correct rate {1/current_should_be:.9f}: ${inr_amount * (1/current_should_be):.2f} USD (CORRECT!)")
    print(f"Or with rate {current_should_be}: ${inr_amount / current_should_be:.2f} USD (CORRECT!)")

if __name__ == "__main__":
    analyze_odoo_format()
