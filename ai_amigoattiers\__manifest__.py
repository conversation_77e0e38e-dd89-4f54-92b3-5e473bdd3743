{
    'name': 'Website Size Chart & Country Pricing',
    'version': '********.0',
    'category': 'Website/Website',
    'summary': 'Dynamic size charts with gender/brand support and country-based pricing',
    'description': """
        Website Size Chart & Country Pricing Module
        ==========================================

        Features:
        - Dynamic size charts based on gender (Male/Female) and brand
        - Indian sizing standards support
        - Brand-specific fitting variations
        - Image-based unit switching (inch/cm)
        - Country-based pricing popup
        - Responsive popup modals

        Note: This module provides the framework without demo data.
        Configure your own size charts and country pricing as needed.
    """,
    'author': 'ArihantAi',
    'website': 'https://www.arihantai.com',
    'depends': [
        'base',
        'web',
        'website',
        'website_sale',
        'product',
        'sale',
        'ai_currencyrateupdate',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/product_template_views.xml',
        'views/size_chart_templates.xml',
        'views/country_selection_templates.xml',
        'views/test_currency_page.xml',
        'views/test_pages.xml',
        'views/module_overview.xml',
        # 'data/size_chart_migration.xml',  # Temporarily disabled for module upgrade
        # 'data/demo_size_charts.xml',  # Demo size charts disabled - using UI created data
    ],
    'assets': {
        'web.assets_frontend': [
            'ai_amigoattiers/static/src/css/modals.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
}

