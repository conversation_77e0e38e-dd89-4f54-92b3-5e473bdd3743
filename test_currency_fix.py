#!/usr/bin/env python3
"""
Test script to verify the currency rate calculation fix
"""

def test_currency_calculations():
    print("=== Testing Fixed Currency Rate Calculations ===")
    print()
    
    # Simulate OpenExchangeRates API response
    api_response = {
        'base': 'USD',
        'rates': {
            'INR': 85.94,  # 1 USD = 85.94 INR
            'EUR': 0.85,   # 1 USD = 0.85 EUR
            'GBP': 0.75,   # 1 USD = 0.75 GBP
            'JPY': 150.0,  # 1 USD = 150 JPY
        }
    }
    
    # Test scenario: Company currency is INR
    base_currency_code = api_response['base']  # USD
    company_currency_code = 'INR'
    convert_rate = api_response['rates'][company_currency_code]  # 85.94
    
    print(f"API Base Currency: {base_currency_code}")
    print(f"Company Currency: {company_currency_code}")
    print(f"Convert Rate (1 {base_currency_code} = {convert_rate} {company_currency_code})")
    print()
    
    # Test each currency calculation
    currencies_to_test = ['USD', 'EUR', 'GBP', 'JPY']
    
    for currency_code in currencies_to_test:
        print(f"=== Calculating rate for {currency_code} ===")
        
        if currency_code in api_response['rates']:
            # Currency is in the rates (not the base currency)
            if base_currency_code == company_currency_code:
                # Company currency is same as API base (e.g., both USD)
                rate = 1.0 / api_response['rates'][currency_code]
                print(f"Branch: Company = API base")
            else:
                # Company currency is different from API base
                if currency_code == base_currency_code:
                    # We want USD rate when company currency is INR
                    rate = 1.0 / convert_rate
                    print(f"Branch: Target currency is API base")
                else:
                    # For other currencies, convert through USD
                    rate = 1.0 / (convert_rate * api_response['rates'][currency_code])
                    print(f"Branch: Convert through API base")
        elif currency_code == base_currency_code:
            # Special case: we want the base currency rate
            if base_currency_code != company_currency_code:
                rate = 1.0 / convert_rate
                print(f"Branch: Special case - base currency")
            else:
                print(f"Branch: Skip (company currency = base currency)")
                continue
        else:
            print(f"Currency {currency_code} not found in API response")
            continue
            
        print(f"Calculated rate: {rate:.8f}")
        print(f"Meaning: 1 {company_currency_code} = {rate:.8f} {currency_code}")
        
        # Verify with manual calculation
        if currency_code == 'USD':
            expected = 1.0 / 85.94  # 1 INR = 1/85.94 USD
            print(f"Expected: {expected:.8f}")
            print(f"Match: {'✅' if abs(rate - expected) < 0.000001 else '❌'}")
        elif currency_code == 'EUR':
            # 1 INR = (1/85.94) USD = (1/85.94) * (1/0.85) EUR
            expected = 1.0 / (85.94 * 0.85)
            print(f"Expected: {expected:.8f}")
            print(f"Match: {'✅' if abs(rate - expected) < 0.000001 else '❌'}")
        
        print()
    
    print("=== Summary ===")
    print("With the fix:")
    print("• ₹1 INR should show as $0.0116 USD (correct!)")
    print("• ₹1 INR should show as €0.0137 EUR")
    print("• ₹1000 INR should show as $11.64 USD")
    print()
    print("Before the fix:")
    print("• ₹1 INR was showing as $1.43 USD (wrong!)")

if __name__ == "__main__":
    test_currency_calculations()
