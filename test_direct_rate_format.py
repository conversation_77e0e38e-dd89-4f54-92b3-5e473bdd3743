#!/usr/bin/env python3
"""
Test the CORRECT Odoo rate format - DIRECT rates (not inverse)
"""

def test_direct_format():
    print("=== TESTING CORRECT ODOO RATE FORMAT - DIRECT RATES ===")
    print("Odoo expects direct exchange rates and handles display automatically")
    print()
    
    # API response simulation
    api_rates = {
        'INR': 85.94,  # 1 USD = 85.94 INR
        'EUR': 0.85,   # 1 USD = 0.85 EUR
        'GBP': 0.75,   # 1 USD = 0.75 GBP
        'CAD': 1.35,   # 1 USD = 1.35 CAD
        'AED': 3.67,   # 1 USD = 3.67 AED
        'FJD': 2.25    # 1 USD = 2.25 FJD
    }
    
    company_currency_code = 'INR'
    base_currency_code = 'USD'
    company_to_usd_rate = api_rates[company_currency_code]  # 85.94
    
    print("API Rates:")
    for currency, rate in api_rates.items():
        print(f"  1 USD = {rate} {currency}")
    print()
    
    print("=== CORRECT ODOO RATE CALCULATIONS (DIRECT FORMAT) ===")
    
    currencies = ['USD', 'EUR', 'GBP', 'CAD', 'AED', 'FJD']
    
    for currency_code in currencies:
        if currency_code == company_currency_code:
            continue
            
        print(f"--- {currency_code} ---")
        
        if currency_code == base_currency_code:
            # USD: Store direct rate
            # API: 1 USD = 85.94 INR
            # Odoo: store 85.94 (direct rate)
            odoo_rate = company_to_usd_rate
            print(f"API: 1 USD = {company_to_usd_rate} INR")
            print(f"Store in Odoo: {odoo_rate}")
            print(f"Odoo will display:")
            print(f"  'Unit per INR': {1/odoo_rate:.9f}")
            print(f"  'INR per Unit': {odoo_rate}")
            
        elif currency_code in api_rates:
            # Other currencies: Calculate direct cross rate
            # API: 1 USD = 0.85 EUR, 1 USD = 85.94 INR
            # We want: 1 EUR = ? INR
            # Answer: 1 EUR = (85.94 INR/USD) / (0.85 EUR/USD) = 101.11 INR
            api_rate = api_rates[currency_code]
            odoo_rate = company_to_usd_rate / api_rate
            print(f"API: 1 USD = {api_rate} {currency_code}")
            print(f"Cross calculation: {company_to_usd_rate} ÷ {api_rate} = {odoo_rate:.6f}")
            print(f"Store in Odoo: {odoo_rate:.6f}")
            print(f"Meaning: 1 {currency_code} = {odoo_rate:.6f} INR")
            print(f"Odoo will display:")
            print(f"  'Unit per INR': {1/odoo_rate:.9f}")
            print(f"  'INR per Unit': {odoo_rate:.6f}")
        
        print()
    
    print("=== EXPECTED ODOO DISPLAY ===")
    print("After fix, your Odoo should show:")
    print("Currency | Unit per INR | INR per Unit")
    print("---------|--------------|-------------")
    
    for currency_code in ['USD', 'EUR', 'GBP', 'CAD', 'AED', 'FJD']:
        if currency_code == 'USD':
            rate = company_to_usd_rate  # 85.94
        else:
            rate = company_to_usd_rate / api_rates[currency_code]
        
        unit_per_inr = 1 / rate
        inr_per_unit = rate
        
        print(f"{currency_code:8} | {unit_per_inr:12.9f} | {inr_per_unit:11.6f}")
    
    print()
    print("=== VERIFICATION: 889 INR to USD ===")
    inr_amount = 889
    usd_rate = 85.94  # Direct rate stored in Odoo
    
    # Odoo conversion: INR amount × (Unit per INR)
    unit_per_inr = 1 / usd_rate  # 0.*********
    usd_amount = inr_amount * unit_per_inr
    
    print(f"₹{inr_amount} INR × {unit_per_inr:.9f} (Unit per INR) = ${usd_amount:.2f} USD")
    print("✅ This should give ~$10.35 USD (CORRECT!)")
    print()
    
    print("=== COMPARISON WITH CURRENT WRONG RATES ===")
    current_wrong_rates = {
        'USD': {'unit_per_inr': 0.000194, 'inr_per_unit': 5153.080674},
        'EUR': {'unit_per_inr': 0.000143, 'inr_per_unit': 7016.282578},
        'AED': {'unit_per_inr': 0.000713, 'inr_per_unit': 1402.947061}
    }
    
    print("Current WRONG vs Expected CORRECT:")
    for currency in ['USD', 'EUR', 'AED']:
        if currency == 'USD':
            expected_rate = 85.94
        elif currency == 'EUR':
            expected_rate = 85.94 / 0.85  # 101.11
        elif currency == 'AED':
            expected_rate = 85.94 / 3.67  # 23.42
            
        expected_unit_per_inr = 1 / expected_rate
        
        current = current_wrong_rates[currency]
        
        print(f"{currency}:")
        print(f"  Unit per INR: {current['unit_per_inr']:.9f} → {expected_unit_per_inr:.9f}")
        print(f"  INR per Unit: {current['inr_per_unit']:.1f} → {expected_rate:.2f}")
        print()

if __name__ == "__main__":
    test_direct_format()
