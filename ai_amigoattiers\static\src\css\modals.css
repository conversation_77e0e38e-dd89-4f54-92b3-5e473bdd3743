/* Size Chart Modal Styles */
.size-chart-modal .modal-dialog {
    max-width: 900px;
}

.unit-toggle .btn {
    border-radius: 20px;
    margin: 0 5px;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.unit-toggle .btn.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.unit-icon {
    width: 16px;
    height: 16px;
    margin-right: 5px;
}

/* Myntra-style Size Chart */
.size-chart-table {
    border-collapse: collapse;
    width: 100%;
    margin-top: 15px;
}

.size-chart-table th {
    background-color: #f5f5f6;
    color: #282c3f;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 12px;
    padding: 12px 8px;
}

.size-chart-table td {
    border: 1px solid #eaeaec;
    padding: 12px 8px;
    text-align: center;
    color: #282c3f;
}

.size-chart-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.size-chart-table .font-weight-bold {
    background-color: #ff3f6c;
    color: white;
    font-weight: 600;
}

.unit-toggle {
    margin-bottom: 20px;
}

.unit-toggle .btn {
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    border: 1px solid #ff3f6c;
    color: #ff3f6c;
    background-color: white;
}

.unit-toggle .btn.active {
    background-color: #ff3f6c;
    color: white;
}

.measurement-value {
    position: relative;
}

.cm-value, .inch-value {
    display: inline-block;
    transition: opacity 0.3s ease;
}

.size-guide-description {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #007bff;
}

/* Country Selection Modal Styles */
#countrySelectionModal .modal-dialog {
    max-width: 600px;
}

.country-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.country-option {
    cursor: pointer;
    transition: transform 0.2s ease;
}

.country-option:hover {
    transform: translateY(-2px);
}

/* Country Selection Modal - Myntra Style */
.country-card {
    border: 1px solid #eaeaec;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.country-card:hover {
    border-color: #ff3f6c;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.country-option.selected .country-card {
    border-color: #ff3f6c;
    background-color: #fff1f4;
}

.country-flag {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.country-flag-placeholder {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: #6c757d;
}

.country-name {
    font-weight: 600;
    margin-top: 8px;
    color: #282c3f;
}

.country-currency {
    font-size: 12px;
    color: #94969f;
}

/* Current Country Display */
.current-country-display {
    margin-left: 15px;
}

.current-country-display .btn {
    font-size: 13px;
    padding: 5px 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .size-chart-modal .modal-dialog {
        max-width: 95%;
        margin: 10px auto;
    }
    
    .size-chart-table {
        font-size: 12px;
    }
    
    .size-chart-table th,
    .size-chart-table td {
        padding: 8px 4px;
    }
    
    .unit-toggle .btn {
        padding: 6px 12px;
        font-size: 13px;
    }
    
    .country-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
    }
    
    .country-card {
        padding: 15px;
    }
    
    #countrySelectionModal .modal-dialog {
        max-width: 95%;
        margin: 10px auto;
    }
}

@media (max-width: 576px) {
    .size-chart-table th,
    .size-chart-table td {
        padding: 6px 2px;
        font-size: 11px;
    }
    
    .country-grid {
        grid-template-columns: 1fr;
    }
    
    .unit-toggle {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }
    
    .unit-toggle .btn {
        margin: 0;
        width: 100%;
    }
}

/* Animation for modal transitions */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
}

.modal.show .modal-dialog {
    transform: none;
}

/* Loading states */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error states */
.alert-success {
    border-color: #28a745;
    background-color: #d4edda;
    color: #155724;
}

.alert-error {
    border-color: #dc3545;
    background-color: #f8d7da;
    color: #721c24;
}

