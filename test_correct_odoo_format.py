#!/usr/bin/env python3
"""
Test the CORRECT Odoo rate format based on 2010 data analysis
"""

def test_correct_format():
    print("=== TESTING CORRECT ODOO RATE FORMAT ===")
    print("Based on 2010 data: Odoo stores INVERSE rates")
    print()
    
    # Simulate API response
    api_rates = {
        'INR': 85.94,  # 1 USD = 85.94 INR
        'EUR': 0.85,   # 1 USD = 0.85 EUR
        'GBP': 0.75,   # 1 USD = 0.75 GBP
        'CAD': 1.35,   # 1 USD = 1.35 CAD
        'AED': 3.67,   # 1 USD = 3.67 AED
        'FJD': 2.25    # 1 USD = 2.25 FJD
    }
    
    company_currency_code = 'INR'
    base_currency_code = 'USD'
    company_to_usd_rate = api_rates[company_currency_code]  # 85.94
    
    print("API Rates:")
    for currency, rate in api_rates.items():
        print(f"  1 USD = {rate} {currency}")
    print()
    
    print("=== CORRECT ODOO RATE CALCULATIONS (INVERSE FORMAT) ===")
    
    currencies = ['USD', 'EUR', 'GBP', 'CAD', 'AED', 'FJD']
    
    for currency_code in currencies:
        if currency_code == company_currency_code:
            continue
            
        print(f"--- {currency_code} ---")
        
        if currency_code == base_currency_code:
            # USD: Store inverse of exchange rate
            # API: 1 USD = 85.94 INR
            # Odoo: 1/85.94 = 0.01164 (means 1 INR = 0.01164 USD)
            odoo_rate = 1.0 / company_to_usd_rate
            print(f"API: 1 USD = {company_to_usd_rate} INR")
            print(f"Odoo rate: {odoo_rate:.9f}")
            print(f"Meaning: 1 INR = {odoo_rate:.9f} USD")
            
        elif currency_code in api_rates:
            # Other currencies: Calculate inverse cross rate
            # API: 1 USD = 0.85 EUR, 1 USD = 85.94 INR
            # We want: How many EUR = 1 INR?
            # Answer: 1 INR = (0.85 EUR/USD) / (85.94 INR/USD) = 0.00989 EUR
            api_rate = api_rates[currency_code]
            odoo_rate = api_rate / company_to_usd_rate
            print(f"API: 1 USD = {api_rate} {currency_code}")
            print(f"Cross calculation: {api_rate} ÷ {company_to_usd_rate} = {odoo_rate:.9f}")
            print(f"Odoo rate: {odoo_rate:.9f}")
            print(f"Meaning: 1 INR = {odoo_rate:.9f} {currency_code}")
        
        print()
    
    print("=== COMPARISON WITH 2010 FORMAT ===")
    print("2010 USD rate: 0.********* (when 1 USD ≈ 76.48 INR)")
    print("Expected calculation: 1/76.48 = 0.*********")
    print("✅ Matches! This confirms inverse format")
    print()
    
    print("=== EXPECTED NEW RATES ===")
    print("After fix, your Odoo should show:")
    print(f"USD: {1.0/85.94:.9f} (not 1.432657)")
    print(f"EUR: {0.85/85.94:.9f} (not 1.950664)")
    print(f"GBP: {0.75/85.94:.9f} (not 1.950664)")
    print(f"CAD: {1.35/85.94:.9f} (not 1.049157)")
    print(f"AED: {3.67/85.94:.9f} (not 0.390047)")
    print(f"FJD: {2.25/85.94:.9f} (not 0.636807)")
    print()
    
    print("=== VERIFICATION: 889 INR to USD ===")
    inr_amount = 889
    correct_usd_rate = 1.0 / 85.94  # 0.*********
    
    # With correct inverse rate
    usd_amount = inr_amount * correct_usd_rate
    print(f"₹{inr_amount} INR × {correct_usd_rate:.9f} = ${usd_amount:.2f} USD")
    print("✅ This should give ~$10.35 USD (CORRECT!)")
    print()
    
    # Verify reverse calculation
    print("=== REVERSE VERIFICATION ===")
    print("If someone enters $10.35 USD, should show ₹889 INR:")
    usd_input = 10.35
    inr_result = usd_input / correct_usd_rate
    print(f"${usd_input} USD ÷ {correct_usd_rate:.9f} = ₹{inr_result:.0f} INR")
    print("✅ This confirms the format is correct!")

if __name__ == "__main__":
    test_correct_format()
