# Deployment Instructions for Country Popup Fix

## Issues Fixed

1. **Assets not loading**: Uncommented the assets section in `__manifest__.py`
2. **Template conflicts**: Removed duplicate `country_selection_template.xml`
3. **Missing security access**: Added access rights for `website.country.pricing` model
4. **Dynamic loading**: Added JavaScript functions to load countries via AJAX

## Files Changed

1. `ai_amigoattiers/__manifest__.py` - Enabled assets loading
2. `ai_amigoattiers/security/ir.model.access.csv` - Added country pricing access
3. `ai_amigoattiers/models/country_pricing.py` - Updated to show all countries
4. `ai_amigoattiers/controllers/website.py` - Enhanced controller with error handling
5. `ai_amigoattiers/static/src/js/country_pricing.js` - Added dynamic loading
6. `ai_amigoattiers/views/country_popup_templates.xml` - Fixed template inheritance
7. Removed: `ai_amigoattiers/views/country_selection_template.xml` - Duplicate template

## Deployment Steps

### Option 1: Module Update (Recommended)
```bash
# 1. Update the module files on the server
# 2. Restart Odoo service
sudo systemctl restart odoo

# 3. Update the module via Odoo interface:
# - Go to Apps
# - Search for "Website Size Chart & Country Pricing"
# - Click "Upgrade"
```

### Option 2: Manual Database Update
```bash
# If module update doesn't work, try:
# 1. Update module files
# 2. Restart Odoo
# 3. Update module list and upgrade
```

## Testing

1. **Test URL**: `https://amigoattires.com/test/country-popup`
2. **Check browser console** for JavaScript errors
3. **Test AJAX endpoint**: `/shop/get_countries`
4. **Verify modal opens** and countries load

## Expected Behavior

1. Country popup should appear automatically on first visit
2. All countries should be listed with appropriate currencies
3. Countries without active currency should default to USD
4. Selection should work and save to session
5. Page should reload after country selection

## Troubleshooting

### If popup still doesn't load:
1. Check browser console for JavaScript errors
2. Verify assets are loading: `/web/assets/...`
3. Check if module is properly installed and upgraded
4. Clear browser cache and try again

### If countries don't load:
1. Test the endpoint directly: `/shop/get_countries`
2. Check Odoo logs for Python errors
3. Verify database has countries and currencies
4. Check security access permissions

### If template not found:
1. Verify template inheritance is working
2. Check if `website.layout` template exists
3. Restart Odoo service
4. Update module again

## Debug Commands

```javascript
// Test in browser console:
fetch('/shop/get_countries', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({jsonrpc: '2.0', method: 'call', params: {}, id: 1})
})
.then(r => r.json())
.then(d => console.log(d));
```

## Contact

If issues persist, check:
1. Odoo server logs
2. Browser developer tools
3. Module installation status
4. Database integrity
