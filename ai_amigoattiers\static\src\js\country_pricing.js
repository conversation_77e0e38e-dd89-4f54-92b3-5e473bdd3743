odoo.define('ai_amigoattiers.country_pricing', function (require) {
'use strict';

var publicWidget = require('web.public.widget');
var ajax = require('web.ajax');

/**
 * Country Pricing Widget
 *
 * This widget handles the country selection modal that appears to users for:
 * 1. Selecting their country for localized pricing
 * 2. Setting appropriate currency based on country selection
 * 3. Updating price lists to match selected currency
 * 4. Storing user preferences to avoid repeated prompts
 *
 * The widget integrates with Odoo's website sale module and currency system
 * to provide a seamless international shopping experience.
 */
publicWidget.registry.CountryPricing = publicWidget.Widget.extend({
    selector: '#countrySelectionModal',
    events: {
        'click .country-option': '_onCountrySelect',           // Handle country selection clicks
        'click #continueWithoutSelection': '_onContinueWithoutSelection', // Handle skip option
        'show.bs.modal': '_onModalShow',                       // Handle modal display events
    },

    /**
     * Widget initialization
     * @override
     */
    start: function () {
        this._super.apply(this, arguments);
        this.selectedCountry = null;  // Track currently selected country
        return this._checkAutoShow(); // Check if modal should auto-display
    },

    /**
     * Check if country selection modal should be automatically shown
     *
     * The modal is shown on first visit to help users select their preferred
     * country and currency. Uses localStorage to track if user has already
     * made a selection to avoid repeated prompts.
     *
     * @returns {Promise} Resolves when check is complete
     */
    _checkAutoShow: function () {
        // Show country selection on first visit only
        if (!localStorage.getItem('country_selection_shown')) {
            // Wait for page to load completely before showing modal
            // This ensures all page elements are ready and prevents layout issues
            setTimeout(function () {
                $('#countrySelectionModal').modal('show');
            }, 1000);
        }
        return Promise.resolve();
    },

    /**
     * Handle country selection click event
     *
     * When a user clicks on a country option in the modal:
     * 1. Prevents default link behavior
     * 2. Highlights the selected country visually
     * 3. Stores the selection internally
     * 4. Makes AJAX call to set country on server
     * 5. Closes the modal
     *
     * @param {Event} ev - Click event from country option
     */
    _onCountrySelect: function (ev) {
        ev.preventDefault();
        var $option = $(ev.currentTarget);
        var countryId = $option.data('country-id');

        // Highlight selected country by removing previous selection
        // and adding 'selected' class to current option
        this.$('.country-option').removeClass('selected');
        $option.addClass('selected');

        // Store selected country for internal tracking
        this.selectedCountry = countryId;

        // Make server call to set country and update currency/pricing
        this._setCountry(countryId);

        // Close the modal since selection is complete
        $('#countrySelectionModal').modal('hide');
    },

    /**
     * Set country on server via AJAX call
     *
     * This method communicates with the backend to:
     * 1. Set the selected country in the user's session
     * 2. Update the currency based on country selection
     * 3. Create/update appropriate price lists
     * 4. Store user preference to avoid future prompts
     *
     * @param {number} countryId - ID of the selected country
     * @returns {Promise} AJAX promise for the server call
     */
    _setCountry: function (countryId) {
        var self = this;

        return ajax.jsonRpc('/shop/set_country', 'call', {
            'country_id': countryId
        }).then(function (data) {
            if (data.success) {
                // Mark that user has made a country selection
                // This prevents the modal from auto-showing on future visits
                localStorage.setItem('country_selection_shown', 'true');

                // Update the country display in the website header
                self._updateCountryDisplay(data.country);

                // Reload page to apply new pricing and currency changes
                // This ensures all prices are recalculated with new currency
                window.location.reload();
            } else if (data.error) {
                console.error('Error setting country:', data.error);
            }
        }).catch(function (error) {
            console.error('Failed to set country:', error);
        });
    },

    /**
     * Update country display in website header
     *
     * Updates the header elements to show the newly selected country
     * and its associated currency symbol. This provides visual feedback
     * to users about their current country/currency selection.
     *
     * @param {Object} country - Country object with name and currency_symbol
     */
    _updateCountryDisplay: function (country) {
        var $display = $('.current-country-display');
        if ($display.length) {
            // Update country name in header display
            $display.find('.country-name').text(country.name);
            // Update currency symbol in header display
            $display.find('.currency-symbol').text(country.currency_symbol);
        }
    },

    /**
     * Handle "Continue without selection" button click
     *
     * When users choose to skip country selection:
     * 1. Prevents default button behavior
     * 2. Sets a cookie to remember this choice for 30 days
     * 3. Closes the modal
     *
     * This allows users to browse with default settings (usually INR)
     * without being prompted again for a month.
     *
     * @param {Event} ev - Click event from continue button
     */
    _onContinueWithoutSelection: function (ev) {
        ev.preventDefault();
        // Remember user's choice to skip selection for 30 days
        this._setCookie('country_popup_shown', 'true', 30);
        // Close the modal
        this.$el.modal('hide');
    },

    /**
     * Save country selection and update pricing (Alternative method)
     *
     * This is an alternative method for saving country selection that:
     * 1. Validates that a country is selected
     * 2. Shows loading state during processing
     * 3. Makes AJAX call to save selection
     * 4. Updates cookies for persistence
     * 5. Updates page pricing immediately
     * 6. Shows success/error messages
     * 7. Reloads page after delay for full price update
     *
     * Note: This method appears to be an alternative to _setCountry
     * and may be used in different contexts or as a fallback.
     */
    _saveCountrySelection: function () {
        // Exit early if no country is selected
        if (!this.selectedCountry) {
            return;
        }

        var self = this;

        // Show loading indicator to user
        this._showLoading();

        // Make AJAX call to save country selection on server
        ajax.jsonRpc('/shop/set_country', 'call', {
            'country_code': this.selectedCountry.code,
        }).then(function (result) {
            if (result.success) {
                // Store selection in cookies for persistence across sessions
                self._setCookie('selected_country', self.selectedCountry.code, 365);
                self._setCookie('country_popup_shown', 'true', 365);

                // Update pricing on current page immediately
                self._updatePagePricing(result.pricing_data);

                // Close the modal
                self.$el.modal('hide');

                // Show success feedback to user
                self._showSuccessMessage();

                // Reload page after delay to ensure all prices are updated
                // The delay allows user to see the success message
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                // Show error message if server returns failure
                self._showError(result.error || 'Failed to save country selection');
            }
        }).catch(function (error) {
            // Handle network or other errors
            console.error('Country selection error:', error);
            self._showError('Failed to save country selection. Please try again.');
        }).finally(function () {
            // Always hide loading state when done
            self._hideLoading();
        });
    },

    /**
     * Update pricing display on current page
     *
     * Since Odoo handles all pricing through its currency and pricelist system,
     * we don't need to manually convert prices in JavaScript. Instead, we just
     * update the header display and let Odoo handle price calculations.
     *
     * @param {Object} pricingData - Contains country_name and currency_symbol
     */
    _updatePagePricing: function (pricingData) {
        if (!pricingData) return;

        // Only update the header display - let Odoo handle all price conversions
        // through its built-in currency and pricelist system
        this._updateHeaderCurrency(pricingData);

        // Note: Prices will be updated when the page reloads, which happens
        // after country selection is complete
    },

    /**
     * Update header currency display
     *
     * Updates the header button that shows current country and currency.
     * This provides visual feedback about the active selection.
     *
     * @param {Object} pricingData - Contains country_name and currency_symbol
     */
    _updateHeaderCurrency: function (pricingData) {
        var $countryDisplay = $('.current-country-display');
        if ($countryDisplay.length) {
            // Create display text showing country and currency
            var displayText = pricingData.country_name + ' (' + pricingData.currency_symbol + ')';
            // Update button content with globe icon and country/currency info
            $countryDisplay.find('button').html('<i class="fa fa-globe"></i> ' + displayText);
        }
    },

    /**
     * Show loading state during AJAX operations
     *
     * Displays a loading overlay to prevent user interaction
     * and provide visual feedback during server communication.
     */
    _showLoading: function () {
        this.$('.modal-body').append('<div class="loading-overlay"><div class="loading-spinner"></div></div>');
    },

    /**
     * Hide loading state
     *
     * Removes the loading overlay when operations complete.
     */
    _hideLoading: function () {
        this.$('.loading-overlay').remove();
    },

    /**
     * Show success message to user
     *
     * Displays a temporary success message when country selection
     * is saved successfully. Message auto-fades after 2 seconds.
     */
    _showSuccessMessage: function () {
        var message = '<div class="alert alert-success text-center">' +
                     '<i class="fa fa-check-circle"></i> Country selection saved! Updating prices...' +
                     '</div>';

        // Add message to modal
        this.$('.modal-body').prepend(message);

        // Auto-hide message after 2 seconds
        setTimeout(() => {
            this.$('.alert-success').fadeOut();
        }, 2000);
    },

    /**
     * Show error message to user
     *
     * Displays an error message when operations fail.
     * Message auto-fades after 3 seconds.
     *
     * @param {string} message - Error message to display
     */
    _showError: function (message) {
        var errorHtml = '<div class="alert alert-error text-center">' +
                       '<i class="fa fa-exclamation-triangle"></i> ' + message +
                       '</div>';

        // Add error message to modal
        this.$('.modal-body').prepend(errorHtml);

        // Auto-hide error message after 3 seconds
        setTimeout(() => {
            this.$('.alert-error').fadeOut();
        }, 3000);
    },

    /**
     * Handle modal show event
     *
     * Called when the country selection modal is displayed.
     * Triggers country loading and analytics tracking.
     */
    _onModalShow: function () {
        // Load available countries from server
        this._loadCountries();

        // Track modal display for analytics (if Google Analytics is available)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'country_popup_shown', {
                'event_category': 'engagement',
                'event_label': 'country_selection'
            });
        }
    },

    /**
     * Load countries dynamically via AJAX
     *
     * Fetches the list of available countries from the server.
     * Countries are loaded dynamically to ensure up-to-date data
     * and to support different country configurations per website.
     *
     * @returns {Promise} AJAX promise for country loading
     */
    _loadCountries: function () {
        var self = this;
        var $countryGrid = this.$('#countryGrid');

        // Show loading indicator while fetching countries
        $countryGrid.html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading countries...</div>');

        return ajax.jsonRpc('/shop/get_countries', 'call', {}).then(function (data) {
            if (data.success && data.countries) {
                // Render countries if successfully loaded
                self._renderCountries(data.countries);
            } else {
                // Show error message if loading failed
                $countryGrid.html('<div class="text-center text-danger">Failed to load countries. Please refresh the page.</div>');
                console.error('Failed to load countries:', data.error);
            }
        }).catch(function (error) {
            // Handle network or other errors
            $countryGrid.html('<div class="text-center text-danger">Failed to load countries. Please refresh the page.</div>');
            console.error('Error loading countries:', error);
        });
    },

    /**
     * Render countries in the grid layout
     *
     * Creates HTML for country options and displays them in a grid.
     * Each country shows its name, currency, and flag (if available).
     *
     * @param {Array} countries - Array of country objects from server
     */
    _renderCountries: function (countries) {
        var $countryGrid = this.$('#countryGrid');
        var html = '';

        // Generate HTML for each country option
        countries.forEach(function (country) {
            html += '<div class="country-option" data-country-id="' + country.id + '">';
            html += '  <div class="country-card">';

            // Display country flag if available, otherwise show placeholder
            if (country.flag_url) {
                html += '    <img src="' + country.flag_url + '" alt="' + country.name + '" class="country-flag mb-2"/>';
            } else {
                html += '    <div class="country-flag-placeholder mb-2"><i class="fa fa-globe"></i></div>';
            }

            // Display country name
            html += '    <div class="country-name">' + country.name + '</div>';

            // Display currency code and symbol
            html += '    <div class="country-currency text-muted">' + country.currency;
            if (country.currency_symbol) {
                html += ' (' + country.currency_symbol + ')';
            }
            html += '</div>';
            html += '  </div>';
            html += '</div>';
        });

        // Insert generated HTML into the country grid
        $countryGrid.html(html);
    },

    /**
     * Set browser cookie with expiration
     *
     * Utility method for storing user preferences in cookies.
     * Used to remember country selection and modal display preferences.
     *
     * @param {string} name - Cookie name
     * @param {string} value - Cookie value
     * @param {number} days - Expiration in days
     */
    _setCookie: function (name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    },

    /**
     * Get browser cookie value
     *
     * Utility method for retrieving stored user preferences.
     *
     * @param {string} name - Cookie name to retrieve
     * @returns {string|null} Cookie value or null if not found
     */
    _getCookie: function (name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    },
});

/**
 * Country Change Widget (for header dropdown)
 *
 * This widget handles the country change button in the website header.
 * It allows users to reopen the country selection modal to change
 * their previously selected country and currency.
 */
publicWidget.registry.CountryChange = publicWidget.Widget.extend({
    selector: '.current-country-display',
    events: {
        'click [data-target="#countrySelectionModal"]': '_onChangeCountryClick',
    },

    /**
     * Handle change country click from header
     *
     * When users click the country/currency display in the header,
     * this method tracks the interaction and allows Bootstrap to
     * show the country selection modal.
     *
     * @param {Event} ev - Click event from header country button
     */
    _onChangeCountryClick: function (ev) {
        ev.preventDefault();

        // Track user interaction for analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'change_country_clicked', {
                'event_category': 'engagement',
                'event_label': 'header_country_change'
            });
        }

        // Modal will be shown automatically by Bootstrap data-target attribute
        // No additional action needed here
    },
});

/**
 * Country Auto-Detection Widget (Optional Enhancement)
 *
 * This widget provides optional functionality to automatically detect
 * a user's country based on their IP address. Currently implemented
 * as a placeholder for future enhancement.
 *
 * When implemented, this could:
 * 1. Detect user's country via IP geolocation service
 * 2. Pre-select appropriate country in the modal
 * 3. Reduce friction for first-time visitors
 */
publicWidget.registry.CountryAutoDetect = publicWidget.Widget.extend({
    selector: 'body',

    /**
     * Widget initialization
     * @override
     */
    start: function () {
        this._super.apply(this, arguments);

        // Only attempt auto-detection if user hasn't manually selected a country
        if (!this._getCookie('selected_country')) {
            this._detectCountry();
        }
    },

    /**
     * Detect country based on IP address
     *
     * Placeholder method for IP-based country detection.
     * Could be implemented using services like:
     * - ipapi.co
     * - ipgeolocation.io
     * - MaxMind GeoIP
     *
     * For now, relies on manual country selection.
     */
    _detectCountry: function () {
        // Future implementation could make AJAX call to geolocation service
        // and pre-populate country selection based on detected location
        console.log('Country auto-detection could be implemented here');
    },

    /**
     * Get cookie helper method
     *
     * Utility method to check if user has previously selected a country.
     *
     * @param {string} name - Cookie name to retrieve
     * @returns {string|null} Cookie value or null if not found
     */
    _getCookie: function (name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        // Parse through all cookies to find the requested one
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    },
});

// Export the widgets for use by other modules
return {
    CountryPricing: publicWidget.registry.CountryPricing,
    CountryChange: publicWidget.registry.CountryChange,
    CountryAutoDetect: publicWidget.registry.CountryAutoDetect,
};

}); // End of odoo.define

