#!/usr/bin/env python3
"""
Test script to verify correct Odoo currency rate format
This addresses the 889 INR = 1200 USD error
"""

def test_odoo_currency_format():
    print("=== Testing Correct Odoo Currency Rate Format ===")
    print()
    
    # Simulate OpenExchangeRates API response
    api_response = {
        'base': 'USD',
        'rates': {
            'INR': 85.94,  # 1 USD = 85.94 INR
            'EUR': 0.85,   # 1 USD = 0.85 EUR
            'GBP': 0.75,   # 1 USD = 0.75 GBP
        }
    }
    
    print("API Response (USD as base):")
    for currency, rate in api_response['rates'].items():
        print(f"  1 USD = {rate} {currency}")
    print()
    
    # Test scenario: Company currency is INR
    company_currency_code = 'INR'
    base_currency_code = 'USD'
    company_to_usd_rate = api_response['rates'][company_currency_code]  # 85.94
    
    print(f"Company Currency: {company_currency_code}")
    print(f"API Base Currency: {base_currency_code}")
    print(f"Company to USD rate: 1 USD = {company_to_usd_rate} {company_currency_code}")
    print()
    
    print("=== CORRECT ODOO RATE CALCULATION ===")
    print("Odoo rates mean: 'How many company currency units = 1 foreign currency unit'")
    print()
    
    # Calculate rates for each currency
    currencies_to_test = ['USD', 'EUR', 'GBP']
    
    for currency_code in currencies_to_test:
        print(f"=== {currency_code} Rate Calculation ===")
        
        if currency_code == base_currency_code:
            # USD rate when company is INR
            # API: 1 USD = 85.94 INR
            # Odoo rate: 85.94 (means 1 USD = 85.94 INR)
            odoo_rate = company_to_usd_rate
            print(f"USD rate: {odoo_rate}")
            print(f"Meaning: 1 USD = {odoo_rate} INR")
            
        elif currency_code in api_response['rates']:
            # Cross rate calculation
            # API: 1 USD = 85.94 INR, 1 USD = 0.85 EUR
            # We want: 1 EUR = ? INR
            # Answer: 1 EUR = (85.94 INR/USD) / (0.85 EUR/USD) = 101.11 INR
            odoo_rate = company_to_usd_rate / api_response['rates'][currency_code]
            print(f"{currency_code} rate: {odoo_rate:.6f}")
            print(f"Meaning: 1 {currency_code} = {odoo_rate:.6f} INR")
            print(f"Calculation: {company_to_usd_rate} / {api_response['rates'][currency_code]} = {odoo_rate:.6f}")
        
        print()
    
    print("=== VERIFICATION WITH YOUR PROBLEM ===")
    print("Your issue: 889 INR showing as ~1200 USD")
    print()
    
    # With correct USD rate
    usd_rate_correct = 85.94  # 1 USD = 85.94 INR
    inr_amount = 889
    usd_amount_correct = inr_amount / usd_rate_correct
    
    print(f"CORRECT calculation:")
    print(f"₹{inr_amount} INR ÷ {usd_rate_correct} = ${usd_amount_correct:.2f} USD")
    print()
    
    # What was happening before (wrong rate)
    # If Odoo had wrong rate like 0.75 (inverted or wrong calculation)
    wrong_rates = [0.75, 1.0, 1.43]
    
    print("WRONG calculations (what might have been happening):")
    for wrong_rate in wrong_rates:
        wrong_usd = inr_amount / wrong_rate
        print(f"₹{inr_amount} INR ÷ {wrong_rate} = ${wrong_usd:.2f} USD (WRONG!)")
    
    print()
    print("=== SUMMARY ===")
    print("✅ CORRECT: ₹889 INR should show as ~$10.35 USD")
    print("❌ WRONG: ₹889 INR showing as ~$1200 USD indicates wrong rate")
    print()
    print("The fix ensures Odoo rates are stored correctly:")
    print("• USD rate = 85.94 (1 USD = 85.94 INR)")
    print("• EUR rate = 101.11 (1 EUR = 101.11 INR)")
    print("• No division/multiplication errors")
    print("• Proper cross-rate calculations")

if __name__ == "__main__":
    test_odoo_currency_format()
