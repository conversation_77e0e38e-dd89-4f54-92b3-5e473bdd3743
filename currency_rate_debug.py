#!/usr/bin/env python3
"""
Debug script to check currency rate calculations
This will help identify why 1 INR is showing as 1.43 USD instead of ~0.0116 USD
"""

def debug_currency_rates():
    print("=== Currency Rate Debug ===")
    print()
    
    # Simulate OpenExchangeRates API response
    # This is what the API typically returns (USD as base)
    api_response = {
        'base': 'USD',
        'rates': {
            'INR': 85.94,  # 1 USD = 85.94 INR (current real rate)
            'EUR': 0.85,   # 1 USD = 0.85 EUR
            'GBP': 0.75,   # 1 USD = 0.75 GBP
        }
    }
    
    print("API Response (USD as base):")
    print(f"1 USD = {api_response['rates']['INR']} INR")
    print()
    
    # Scenario 1: Company currency is INR (your case)
    print("=== SCENARIO 1: Company Currency = INR ===")
    company_currency = 'INR'
    base_currency = api_response['base']  # USD
    
    print(f"Company Currency: {company_currency}")
    print(f"API Base Currency: {base_currency}")
    print()
    
    # Current (WRONG) calculation in the code
    print("Current code logic:")
    if base_currency == company_currency:
        # This branch won't execute because USD != INR
        print("Branch 1: base_currency == company_currency (USD == INR) -> FALSE")
        print("This branch won't execute")
    else:
        # This is the branch that executes
        print("Branch 2: base_currency != company_currency (USD != INR) -> TRUE")
        convert_rate = api_response['rates'][company_currency]  # 85.94
        print(f"convert_rate = rates['{company_currency}'] = {convert_rate}")
        print("Next line tries: rate = convert_rate / rates['USD']")
        print("ERROR: rates['USD'] doesn't exist in OpenExchangeRates API!")

    print()
    print("ERROR: The current code tries to access rates['USD'] which doesn't exist!")
    print("OpenExchangeRates doesn't include USD in rates because it's the base currency.")
    print()
    
    # CORRECT calculation
    print("=== CORRECT CALCULATION ===")
    
    # For USD rate when company currency is INR:
    # API says: 1 USD = 85.94 INR
    # We want: 1 INR = ? USD
    # Answer: 1 INR = 1/85.94 USD = 0.01164 USD
    
    inr_to_usd_correct = 1.0 / api_response['rates']['INR']
    print(f"Correct rate: 1 INR = {inr_to_usd_correct:.6f} USD")
    print(f"So ₹1 should display as ${inr_to_usd_correct:.4f}")
    print()
    
    # Test with your reported values
    print("=== YOUR REPORTED ISSUE ===")
    print("Product price: ₹1 INR")
    print(f"Should show in USD: ${inr_to_usd_correct:.4f} (about 1 cent)")
    print("Actually showing: $1.43 USD (WRONG!)")
    print()
    
    # Where 1.43 might come from
    print("=== POSSIBLE SOURCE OF 1.43 ===")
    
    # Maybe there's an old/wrong rate in the database
    wrong_rate_scenarios = [
        ("Old EUR rate", 1/0.70),  # ≈ 1.43
        ("Inverted calculation", 85.94/60),  # Some wrong math
        ("Default fallback", 1.43),  # Maybe a hardcoded fallback
    ]
    
    for scenario, value in wrong_rate_scenarios:
        print(f"{scenario}: {value:.4f}")
    
    print()
    print("=== SOLUTION ===")
    print("1. Check what currency rates are currently in your Odoo database")
    print("2. Update the currency rate calculation logic")
    print("3. Ensure the OpenExchangeRates API key is configured")
    print("4. Run a fresh currency rate update")

if __name__ == "__main__":
    debug_currency_rates()
