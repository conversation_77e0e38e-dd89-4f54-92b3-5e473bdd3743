<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Scheduled Action for Currency Rate Update -->
        <record id="ir_cron_currency_rate_update" model="ir.cron">
            <field name="name">Update Currency Rates (OpenExchangeRates)</field>
            <field name="model_id" ref="model_currency_rate_update"/>
            <field name="state">code</field>
            <field name="code">model.cron_update_currency_rates()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="priority">1</field>
            <field name="user_id" ref="base.user_root"/>
            <field name="active" eval="True"/>
        </record>
    </data>
</odoo> 