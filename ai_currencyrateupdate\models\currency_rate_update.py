import requests
import logging
from datetime import datetime
from odoo import models, api, fields

_logger = logging.getLogger(__name__)

class CurrencyRateUpdate(models.Model):
    _name = 'currency.rate.update'
    _description = 'Currency Rate Update using OpenExchangeRates API'
    _order = 'create_date desc'

    name = fields.Char(string='Name', compute='_compute_name', store=True)
    success = fields.Boolean(string='Success', default=False)
    message = fields.Text(string='Message', readonly=True)
    
    @api.depends('create_date')
    def _compute_name(self):
        for record in self:
            if record.create_date:
                record.name = f"Update - {record.create_date.strftime('%Y-%m-%d %H:%M:%S')}"
            else:
                record.name = "New Update"

    def update_currency_rates(self):
        """
        Update currency rates from OpenExchangeRates API
        This method is called from button action or scheduled action
        """
        try:
            # Get API configuration from system parameters
            ICP = self.env['ir.config_parameter'].sudo()
            api_key = ICP.get_param('openexchangerates.api_key', False)
            
            if not api_key:
                message = "OpenExchangeRates API key is not configured"
                _logger.error(message)
                self.write({'success': False, 'message': message})
                return {'type': 'ir.actions.act_window_close'}
            
            # API endpoint
            url = f"https://openexchangerates.org/api/latest.json?app_id={api_key}"
            
            # Make API request
            response = requests.get(url)
            response.raise_for_status()
            
            # Parse response data
            data = response.json()
            base_currency_code = data.get('base')  # Usually USD from OpenExchangeRates
            rates = data.get('rates', {})
            timestamp = data.get('timestamp')
            date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
            
            if not rates:
                message = "No rates returned from OpenExchangeRates API"
                _logger.error(message)
                self.write({'success': False, 'message': message})
                return {'type': 'ir.actions.act_window_close'}
            
            # Get currency objects from Odoo
            currency_obj = self.env['res.currency']
            
            # Get base currency from Odoo (company currency)
            company = self.env.company
            company_currency = company.currency_id
            company_currency_code = company_currency.name
            
            # Get all active currencies in Odoo
            active_currencies = currency_obj.search([('active', '=', True)])

            # IMPORTANT: Use direct API rates without division/multiplication
            # OpenExchangeRates provides rates as: 1 USD = X other_currency
            # Odoo expects rates as: 1 company_currency = X other_currency

            # Determine if we need to adjust for company currency
            if base_currency_code == company_currency_code:
                # Company currency is USD (same as API base) - use rates directly
                use_direct_rates = True
                _logger.info(f"Using direct rates (company currency {company_currency_code} = API base {base_currency_code})")
            else:
                # Company currency is not USD - we need to store raw API rates and let Odoo handle conversion
                use_direct_rates = False
                _logger.info(f"Company currency {company_currency_code} differs from API base {base_currency_code}")

                # Verify company currency exists in API response
                if company_currency_code not in rates:
                    message = f"Company currency {company_currency_code} not found in OpenExchangeRates response"
                    _logger.error(message)
                    self.write({'success': False, 'message': message})
                    return {'type': 'ir.actions.act_window_close'}

            updated_count = 0

            # First, handle the API base currency (USD) if it's not the company currency
            if not use_direct_rates and base_currency_code != company_currency_code:
                # Create/update USD rate using the raw API rate for company currency
                usd_currency = currency_obj.search([('name', '=', base_currency_code)], limit=1)
                if usd_currency and usd_currency.active:
                    # Store the raw rate: how many company currency units = 1 USD
                    # API says: 1 USD = 85.94 INR, so we store rate = 85.94
                    raw_rate = rates[company_currency_code]

                    existing_rate = self.env['res.currency.rate'].search([
                        ('currency_id', '=', usd_currency.id),
                        ('name', '=', date)
                    ], limit=1)

                    if existing_rate:
                        existing_rate.rate = raw_rate
                        updated_count += 1
                        _logger.info(f"Updated {base_currency_code} rate: {raw_rate}")
                    else:
                        self.env['res.currency.rate'].create({
                            'currency_id': usd_currency.id,
                            'rate': raw_rate,
                            'name': date
                        })
                        updated_count += 1
                        _logger.info(f"Created {base_currency_code} rate: {raw_rate}")

            # Handle all other currencies
            for currency in active_currencies:
                if currency.id == company_currency.id:
                    continue  # Skip company currency as it's the base (rate=1)

                currency_code = currency.name

                # Skip if we already handled the base currency above
                if currency_code == base_currency_code and not use_direct_rates:
                    continue

                if currency_code in rates:
                    # Store the raw API rate directly - let Odoo handle the conversion
                    raw_rate = rates[currency_code]

                    # Check if there's already a rate for today
                    existing_rate = self.env['res.currency.rate'].search([
                        ('currency_id', '=', currency.id),
                        ('name', '=', date)
                    ], limit=1)

                    if existing_rate:
                        # Update existing rate with raw API rate
                        existing_rate.rate = raw_rate
                        updated_count += 1
                        _logger.info(f"Updated {currency_code} rate: {raw_rate}")
                    else:
                        # Create new rate with raw API rate
                        self.env['res.currency.rate'].create({
                            'currency_id': currency.id,
                            'rate': raw_rate,
                            'name': date
                        })
                        updated_count += 1
                        _logger.info(f"Created {currency_code} rate: {raw_rate}")
                else:
                    _logger.warning(f"Currency {currency_code} not found in OpenExchangeRates response")
            
            message = f"Successfully updated {updated_count} currency rates"
            _logger.info(message)
            self.write({'success': True, 'message': message})
            return {'type': 'ir.actions.act_window_close'}

        except requests.exceptions.RequestException as e:
            message = f"Error fetching currency rates: {str(e)}"
            _logger.error(message)
            self.write({'success': False, 'message': message})
            return {'type': 'ir.actions.act_window_close'}
        except Exception as e:
            message = f"Unexpected error: {str(e)}"
            _logger.error(message)
            self.write({'success': False, 'message': message})
            return {'type': 'ir.actions.act_window_close'}

    @api.model
    def cron_update_currency_rates(self):
        """
        Method to be called by scheduled action (cron job)
        Creates a new record and updates currency rates
        """
        record = self.create({})
        record.update_currency_rates()

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    openexchangerates_api_key = fields.Char(
        string='OpenExchangeRates API Key',
        config_parameter='openexchangerates.api_key',
        help='API Key for OpenExchangeRates service'
    ) 