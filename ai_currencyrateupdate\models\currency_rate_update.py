import requests
import logging
from datetime import datetime
from odoo import models, api, fields

_logger = logging.getLogger(__name__)

class CurrencyRateUpdate(models.Model):
    _name = 'currency.rate.update'
    _description = 'Currency Rate Update using OpenExchangeRates API'
    _order = 'create_date desc'

    name = fields.Char(string='Name', compute='_compute_name', store=True)
    success = fields.Boolean(string='Success', default=False)
    message = fields.Text(string='Message', readonly=True)
    
    @api.depends('create_date')
    def _compute_name(self):
        for record in self:
            if record.create_date:
                record.name = f"Update - {record.create_date.strftime('%Y-%m-%d %H:%M:%S')}"
            else:
                record.name = "New Update"

    def update_currency_rates(self):
        """
        Update currency rates from OpenExchangeRates API
        This method is called from button action or scheduled action
        """
        try:
            # Get API configuration from system parameters
            ICP = self.env['ir.config_parameter'].sudo()
            api_key = ICP.get_param('openexchangerates.api_key', False)
            
            if not api_key:
                message = "OpenExchangeRates API key is not configured"
                _logger.error(message)
                self.write({'success': False, 'message': message})
                return {'type': 'ir.actions.act_window_close'}
            
            # API endpoint
            url = f"https://openexchangerates.org/api/latest.json?app_id={api_key}"
            
            # Make API request
            response = requests.get(url)
            response.raise_for_status()
            
            # Parse response data
            data = response.json()
            base_currency_code = data.get('base')  # Usually USD from OpenExchangeRates
            rates = data.get('rates', {})
            timestamp = data.get('timestamp')
            date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
            
            if not rates:
                message = "No rates returned from OpenExchangeRates API"
                _logger.error(message)
                self.write({'success': False, 'message': message})
                return {'type': 'ir.actions.act_window_close'}
            
            # Get currency objects from Odoo
            currency_obj = self.env['res.currency']
            
            # Get base currency from Odoo (company currency)
            company = self.env.company
            company_currency = company.currency_id
            company_currency_code = company_currency.name
            
            # Get all active currencies in Odoo
            active_currencies = currency_obj.search([('active', '=', True)])

            # CRITICAL: Correct Odoo rate format discovered from 2010 data
            # Odoo stores INVERSE rates: "How many foreign currency units = 1 company currency unit"
            # OpenExchangeRates API provides: "1 USD = X other_currency"
            #
            # Example with company currency INR:
            # - API says: 1 USD = 85.94 INR
            # - For USD rate in Odoo: rate = 1/85.94 = 0.01164 (means 1 INR = 0.01164 USD)
            # - This matches 2010 format: 0.********* when 1 USD = 76.48 INR

            # Get company currency rate from API if needed
            company_to_usd_rate = 1.0
            if company_currency_code != base_currency_code:
                if company_currency_code not in rates:
                    message = f"Company currency {company_currency_code} not found in OpenExchangeRates response"
                    _logger.error(message)
                    self.write({'success': False, 'message': message})
                    return {'type': 'ir.actions.act_window_close'}
                company_to_usd_rate = rates[company_currency_code]
                _logger.info(f"Company currency rate: 1 USD = {company_to_usd_rate} {company_currency_code}")

            updated_count = 0

            # Process all active currencies
            for currency in active_currencies:
                if currency.id == company_currency.id:
                    continue  # Skip company currency as it's the base (rate=1)

                currency_code = currency.name

                # Calculate the correct Odoo INVERSE rate format (like 2010 data)
                if currency_code == base_currency_code:
                    # This is USD - store inverse rate
                    if company_currency_code == base_currency_code:
                        # Company is USD, so USD rate = 1.0 (skip)
                        continue
                    else:
                        # Company is INR, API says 1 USD = 85.94 INR
                        # Odoo rate = 1/85.94 = 0.01164 (means 1 INR = 0.01164 USD)
                        odoo_rate = 1.0 / company_to_usd_rate
                elif currency_code in rates:
                    # This is another currency (EUR, GBP, etc.)
                    # API says: 1 USD = 0.85 EUR, 1 USD = 85.94 INR
                    # We want: How many EUR = 1 INR?
                    # Answer: 1 INR = (0.85 EUR/USD) / (85.94 INR/USD) = 0.00989 EUR
                    if company_currency_code == base_currency_code:
                        # Company is USD, so rate = API_rate
                        # API: 1 USD = 0.85 EUR, so 1 USD = 0.85 EUR (direct)
                        odoo_rate = rates[currency_code]
                    else:
                        # Company is INR, calculate inverse cross rate
                        # 1 INR = (EUR per USD) / (INR per USD) = 0.85 / 85.94 = 0.00989 EUR
                        odoo_rate = rates[currency_code] / company_to_usd_rate
                else:
                    _logger.warning(f"Currency {currency_code} not found in OpenExchangeRates response")
                    continue

                # Store the calculated rate
                existing_rate = self.env['res.currency.rate'].search([
                    ('currency_id', '=', currency.id),
                    ('name', '=', date)
                ], limit=1)

                if existing_rate:
                    existing_rate.rate = odoo_rate
                    updated_count += 1
                    _logger.info(f"Updated {currency_code} rate: {odoo_rate:.9f} (1 {company_currency_code} = {odoo_rate:.9f} {currency_code})")
                else:
                    self.env['res.currency.rate'].create({
                        'currency_id': currency.id,
                        'rate': odoo_rate,
                        'name': date
                    })
                    updated_count += 1
                    _logger.info(f"Created {currency_code} rate: {odoo_rate:.9f} (1 {company_currency_code} = {odoo_rate:.9f} {currency_code})")
            
            message = f"Successfully updated {updated_count} currency rates"
            _logger.info(message)
            self.write({'success': True, 'message': message})
            return {'type': 'ir.actions.act_window_close'}

        except requests.exceptions.RequestException as e:
            message = f"Error fetching currency rates: {str(e)}"
            _logger.error(message)
            self.write({'success': False, 'message': message})
            return {'type': 'ir.actions.act_window_close'}
        except Exception as e:
            message = f"Unexpected error: {str(e)}"
            _logger.error(message)
            self.write({'success': False, 'message': message})
            return {'type': 'ir.actions.act_window_close'}

    @api.model
    def cron_update_currency_rates(self):
        """
        Method to be called by scheduled action (cron job)
        Creates a new record and updates currency rates
        """
        record = self.create({})
        record.update_currency_rates()

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    openexchangerates_api_key = fields.Char(
        string='OpenExchangeRates API Key',
        config_parameter='openexchangerates.api_key',
        help='API Key for OpenExchangeRates service'
    ) 