import requests
import logging
from datetime import datetime
from odoo import models, api, fields

_logger = logging.getLogger(__name__)

class CurrencyRateUpdate(models.Model):
    _name = 'currency.rate.update'
    _description = 'Currency Rate Update using OpenExchangeRates API'
    _order = 'create_date desc'

    name = fields.Char(string='Name', compute='_compute_name', store=True)
    success = fields.Boolean(string='Success', default=False)
    message = fields.Text(string='Message', readonly=True)
    
    @api.depends('create_date')
    def _compute_name(self):
        for record in self:
            if record.create_date:
                record.name = f"Update - {record.create_date.strftime('%Y-%m-%d %H:%M:%S')}"
            else:
                record.name = "New Update"

    def update_currency_rates(self):
        """
        Update currency rates from OpenExchangeRates API
        This method is called from button action or scheduled action
        """
        try:
            # Get API configuration from system parameters
            ICP = self.env['ir.config_parameter'].sudo()
            api_key = ICP.get_param('openexchangerates.api_key', False)
            
            if not api_key:
                message = "OpenExchangeRates API key is not configured"
                _logger.error(message)
                self.write({'success': False, 'message': message})
                return {'type': 'ir.actions.act_window_close'}
            
            # API endpoint
            url = f"https://openexchangerates.org/api/latest.json?app_id={api_key}"
            
            # Make API request
            response = requests.get(url)
            response.raise_for_status()
            
            # Parse response data
            data = response.json()
            base_currency_code = data.get('base')  # Usually USD from OpenExchangeRates
            rates = data.get('rates', {})
            timestamp = data.get('timestamp')
            date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
            
            if not rates:
                message = "No rates returned from OpenExchangeRates API"
                _logger.error(message)
                self.write({'success': False, 'message': message})
                return {'type': 'ir.actions.act_window_close'}
            
            # Get currency objects from Odoo
            currency_obj = self.env['res.currency']
            
            # Get base currency from Odoo (company currency)
            company = self.env.company
            company_currency = company.currency_id
            company_currency_code = company_currency.name
            
            # Get all active currencies in Odoo
            active_currencies = currency_obj.search([('active', '=', True)])
            
            # If base currency from API is different from company currency, we need to convert rates
            convert_rate = 1.0
            if base_currency_code != company_currency_code:
                if company_currency_code in rates:
                    convert_rate = rates[company_currency_code]
                else:
                    message = f"Company currency {company_currency_code} not found in OpenExchangeRates response"
                    _logger.error(message)
                    self.write({'success': False, 'message': message})
                    return {'type': 'ir.actions.act_window_close'}
            
            updated_count = 0
            for currency in active_currencies:
                if currency.id == company_currency.id:
                    continue  # Skip company currency as it's the base (rate=1)
                
                currency_code = currency.name
                
                if currency_code in rates:
                    # Calculate the rate relative to company currency
                    if base_currency_code == company_currency_code:
                        # Company currency is same as API base (e.g., both USD)
                        rate = 1.0 / rates[currency_code]
                    else:
                        # Company currency is different from API base
                        # API base is USD, company currency is something else (e.g., INR)
                        if currency_code == base_currency_code:
                            # We want USD rate when company currency is INR
                            # API: 1 USD = 85.94 INR, so 1 INR = 1/85.94 USD
                            rate = 1.0 / convert_rate
                        else:
                            # For other currencies, convert through USD
                            # Example: EUR when company is INR
                            # API: 1 USD = 85.94 INR, 1 USD = 0.85 EUR
                            # So: 1 INR = (1/85.94) USD = (1/85.94) * (1/0.85) EUR
                            rate = 1.0 / (convert_rate * rates[currency_code])
                elif currency_code == base_currency_code:
                    # Special case: we want the base currency rate (e.g., USD when API base is USD)
                    if base_currency_code != company_currency_code:
                        # Company currency is not USD, so calculate USD rate
                        rate = 1.0 / convert_rate
                    else:
                        # Company currency is USD, so USD rate is 1.0 (skip this currency)
                        continue
                    
                    # Check if there's already a rate for today
                    existing_rate = self.env['res.currency.rate'].search([
                        ('currency_id', '=', currency.id),
                        ('name', '=', date)
                    ], limit=1)
                    
                    if existing_rate:
                        # Update existing rate
                        existing_rate.rate = rate
                        updated_count += 1
                    else:
                        # Create new rate
                        self.env['res.currency.rate'].create({
                            'currency_id': currency.id,
                            'rate': rate,
                            'name': date
                        })
                        updated_count += 1
                else:
                    _logger.warning(f"Currency {currency_code} not found in OpenExchangeRates response")
            
            message = f"Successfully updated {updated_count} currency rates"
            _logger.info(message)
            self.write({'success': True, 'message': message})
            return {'type': 'ir.actions.act_window_close'}

        except requests.exceptions.RequestException as e:
            message = f"Error fetching currency rates: {str(e)}"
            _logger.error(message)
            self.write({'success': False, 'message': message})
            return {'type': 'ir.actions.act_window_close'}
        except Exception as e:
            message = f"Unexpected error: {str(e)}"
            _logger.error(message)
            self.write({'success': False, 'message': message})
            return {'type': 'ir.actions.act_window_close'}

    @api.model
    def cron_update_currency_rates(self):
        """
        Method to be called by scheduled action (cron job)
        Creates a new record and updates currency rates
        """
        record = self.create({})
        record.update_currency_rates()

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    openexchangerates_api_key = fields.Char(
        string='OpenExchangeRates API Key',
        config_parameter='openexchangerates.api_key',
        help='API Key for OpenExchangeRates service'
    ) 