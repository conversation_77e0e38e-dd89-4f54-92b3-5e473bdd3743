{
    'name': 'AI Currency Rate Update',
    'version': '1.2',
    'category': 'Accounting',
    'summary': 'Automatic currency rate updates using OpenExchangeRates API',
    'description': """
        This module provides automatic currency rate updates using the OpenExchangeRates API.
        It adds a scheduled action to automatically update currency rates daily.
    """,
    'depends': ['base', 'account'],
    'data': [
        'security/ir.model.access.csv',
        'views/currency_rate_update_views.xml',
        'views/res_config_settings_views.xml',
        'data/ir_cron_data.xml',
    ],
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
} 