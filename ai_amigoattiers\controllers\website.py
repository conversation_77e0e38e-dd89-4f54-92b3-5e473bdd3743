# -*- coding: utf-8 -*-

from odoo import http, fields
from odoo.http import request
from odoo.addons.website.controllers.main import Website
import json


class WebsiteSizeChartPricing(http.Controller):

    @http.route('/shop/size_chart', type='json', auth='public', website=True)
    def get_size_chart(self, product_id, **kwargs):
        """Get size chart data for a product"""
        try:
            product_id = int(product_id)
            product = request.env['product.template'].browse(product_id)
            
            if not product.exists():
                return {'error': 'Product not found'}
            
            # Get size chart for the product
            size_chart = product.get_size_chart()
            
            if not size_chart:
                return {'error': 'No size chart available for this product'}
            
            # Format size chart data for frontend
            size_chart_data = {
                'id': size_chart.id,
                'name': size_chart.name,
                'brand_name': size_chart.brand_id.name if size_chart.brand_id else '',
                'sizing_standard': size_chart.sizing_standard,
                'description': size_chart.description,
                'fitting_notes': size_chart.brand_id.fitting_notes if size_chart.brand_id else '',
                'sizes': []
            }
            
            # Add size lines
            for line in size_chart.size_line_ids:
                size_chart_data['sizes'].append({
                    'size_name': line.size_name,
                    'size_number': line.size_number,
                    'measurements': {
                        'chest': {'cm': line.chest_cm, 'inch': line.chest_inch},
                        'waist': {'cm': line.waist_cm, 'inch': line.waist_inch},
                        'hip': {'cm': line.hip_cm, 'inch': line.hip_inch},
                        'shoulder': {'cm': line.shoulder_cm, 'inch': line.shoulder_inch},
                        'sleeve': {'cm': line.sleeve_cm, 'inch': line.sleeve_inch},
                        'length': {'cm': line.length_cm, 'inch': line.length_inch}
                    }
                })
            
            return {'size_chart': size_chart_data}
            
        except Exception as e:
            return {'error': str(e)}

    # DISABLED - Country selection functionality removed
    # @http.route('/shop/get_countries', type='json', auth='public', website=True)
    def get_countries_disabled(self):
        """Get available countries for currency selection"""
        try:
            # Get all countries with their currencies
            countries = []
            popular_countries = []

            # Define popular countries (major markets) with their default currencies
            popular_country_data = {
                'IN': 'INR',
                'US': 'USD',
                'GB': 'GBP',
                'CA': 'CAD',
                'AU': 'AUD',
                'DE': 'EUR',
                'FR': 'EUR',
                'SG': 'SGD',
                'AE': 'AED',
                'JP': 'JPY'
            }

            # Get USD as fallback currency
            usd_currency = request.env['res.currency'].search([('name', '=', 'USD')], limit=1)

            # Get all countries
            all_countries = request.env['res.country'].search([])

            for country in all_countries:
                # Get currency for this country based on country code mapping
                currency = None

                # Try to find currency based on country code
                if country.code in popular_country_data:
                    currency_name = popular_country_data[country.code]
                    currency = request.env['res.currency'].search([('name', '=', currency_name)], limit=1)

                # Fallback to USD if no specific currency found
                if not currency:
                    currency = usd_currency

                # Only add countries that have a valid currency
                if currency and currency.active:
                    country_data = {
                        'id': country.id,
                        'name': country.name,
                        'code': country.code,
                        'currency': currency.name,
                        'currency_symbol': currency.symbol or currency.name,
                        'currency_id': currency.id,
                    }

                    countries.append(country_data)

                    # Add to popular if in popular list
                    if country.code in popular_country_data:
                        popular_countries.append(country_data)

            # Sort countries by name
            countries.sort(key=lambda x: x['name'])

            # Sort popular countries by the order in popular_country_codes
            popular_country_codes = list(popular_country_data.keys())
            popular_countries.sort(key=lambda x: popular_country_codes.index(x['code']) if x['code'] in popular_country_codes else 999)

            return {
                'success': True,
                'countries': countries,
                'popular_countries': popular_countries[:8],  # Limit to 8 popular countries
                'total_countries': len(countries)
            }

        except Exception as e:
            import traceback
            error_msg = f"Error: {str(e)}\nTraceback: {traceback.format_exc()}"
            return {'success': False, 'error': error_msg, 'countries': [], 'popular_countries': []}

    # DISABLED - Country selection functionality removed
    # @http.route('/shop/set_country', type='json', auth='public', website=True)
    def set_country_disabled(self, country_code, currency_code=None):
        """Set selected country and currency in session with robust error handling"""
        try:
            # Default to India if no country code provided
            if not country_code:
                country_code = 'IN'

            # Find country by code
            country = request.env['res.country'].search([('code', '=', country_code)], limit=1)
            if not country:
                # Fallback to India if country not found
                country = request.env['res.country'].search([('code', '=', 'IN')], limit=1)
                if not country:
                    return {'success': False, 'error': 'Country not found'}

            # Determine currency with fallbacks
            currency = None

            # If currency_code is provided, try to find it
            if currency_code:
                currency = request.env['res.currency'].search([
                    ('name', '=', currency_code),
                    ('active', '=', True)
                ], limit=1)

            # If no currency found, try country's default currency
            if not currency and country.currency_id and country.currency_id.active:
                currency = country.currency_id

            # Fallback currency mapping for common countries
            if not currency:
                currency_mapping = {
                    'IN': 'INR',
                    'US': 'USD',
                    'GB': 'GBP',
                    'EU': 'EUR',
                    'CA': 'CAD',
                    'AU': 'AUD',
                    'AE': 'AED',
                    'SA': 'SAR',
                    'SG': 'SGD',
                }

                fallback_currency_code = currency_mapping.get(country_code, 'INR')
                currency = request.env['res.currency'].search([
                    ('name', '=', fallback_currency_code),
                    ('active', '=', True)
                ], limit=1)

            # Final fallback to INR
            if not currency:
                currency = request.env['res.currency'].search([
                    ('name', '=', 'INR'),
                    ('active', '=', True)
                ], limit=1)

                # Create INR if it doesn't exist
                if not currency:
                    currency = request.env['res.currency'].create({
                        'name': 'INR',
                        'symbol': '₹',
                        'position': 'before',
                        'active': True,
                        'decimal_places': 2,
                    })

            # Currency rates are updated hourly by cron, so they should be available
            # Ensure we have a pricelist for this currency
            pricelist = self._get_or_create_currency_pricelist(currency)

            # Set country and currency in session
            request.session['website_sale_current_country'] = country.id
            request.session['website_sale_current_currency'] = currency.id

            # Set the pricelist that matches the currency
            if pricelist:
                request.session['website_sale_current_pl'] = pricelist.id

            # Set currency in website context for immediate effect
            if hasattr(request, 'website') and request.website:
                # Update the website currency for this request
                request.website = request.website.with_context(
                    allowed_company_ids=request.website.company_id.ids,
                    website_id=request.website.id,
                    currency_id=currency.id
                )

            return {
                'success': True,
                'country': {
                    'name': country.name,
                    'code': country.code,
                    'currency': currency.name,
                    'currency_symbol': currency.symbol
                },
                'reload_required': True
            }

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error setting country: {e}")

            # Emergency fallback - set India and INR
            try:
                india = request.env['res.country'].search([('code', '=', 'IN')], limit=1)
                inr_currency = request.env['res.currency'].search([('name', '=', 'INR')], limit=1)

                if india and inr_currency:
                    request.session['website_sale_current_country'] = india.id
                    request.session['website_sale_current_currency'] = inr_currency.id

                    return {
                        'success': True,
                        'country': {
                            'name': 'India',
                            'code': 'IN',
                            'currency': 'INR',
                            'currency_symbol': '₹'
                        },
                        'reload_required': True
                    }
            except:
                pass

            return {'success': False, 'error': 'Unable to set country/currency'}

    # DISABLED - Currency application functionality removed
    # @http.route('/shop/apply_currency', type='json', auth='public', website=True)
    def apply_currency_from_storage_disabled(self, currency_code):
        """Apply currency from localStorage"""
        try:
            # Check if currency is already set to avoid repeated processing
            current_currency = request.session.get('website_sale_current_currency')
            if current_currency:
                existing_currency = request.env['res.currency'].browse(current_currency)
                if existing_currency.exists() and existing_currency.name == currency_code:
                    return {'success': True, 'currency': currency_code, 'message': 'Currency already set'}

            # Find currency by code
            currency = request.env['res.currency'].search([('name', '=', currency_code)], limit=1)
            if not currency:
                return {'success': False, 'error': 'Currency not found'}

            # Currency rates are updated hourly by cron, so they should be available

            # Ensure we have a pricelist for this currency
            pricelist = self._get_or_create_currency_pricelist(currency)

            # Set currency in session (using standard Odoo website sale session key)
            request.session['website_sale_current_currency'] = currency.id

            # Set the pricelist that matches the currency
            if pricelist:
                request.session['website_sale_current_pl'] = pricelist.id

            # Also set in website context for immediate effect
            if hasattr(request, 'website') and request.website:
                # Update the website currency for this request
                request.website = request.website.with_context(
                    allowed_company_ids=request.website.company_id.ids,
                    website_id=request.website.id,
                    currency_id=currency.id
                )

            return {'success': True, 'currency': currency.name, 'reload_required': True}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _get_or_create_currency_pricelist(self, currency):
        """Get or create a pricelist for the specified currency"""
        try:
            # First, try to find an existing pricelist with this currency
            existing_pricelist = request.env['product.pricelist'].search([
                ('currency_id', '=', currency.id),
                ('website_id', 'in', [False, request.website.id]),
                ('active', '=', True)
            ], limit=1)

            if existing_pricelist:
                return existing_pricelist

            # If no pricelist exists, create one
            company_currency = request.website.company_id.currency_id

            # Create a new pricelist for this currency
            pricelist_vals = {
                'name': f'{currency.name} Pricelist',
                'currency_id': currency.id,
                'website_id': request.website.id,
                'company_id': request.website.company_id.id,
                'item_ids': [(0, 0, {
                    'applied_on': '3_global',  # Apply to all products
                    'compute_price': 'formula',
                    'base': 'list_price',
                    'price_discount': 0,
                    'price_surcharge': 0,
                    'price_round': 0.01,
                    'price_min_margin': 0,
                    'price_max_margin': 0,
                })]
            }

            new_pricelist = request.env['product.pricelist'].sudo().create(pricelist_vals)

            import logging
            _logger = logging.getLogger(__name__)
            _logger.info(f"Created new pricelist for {currency.name}: {new_pricelist.name}")

            return new_pricelist

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error creating pricelist for {currency.name}: {e}")
            return None

    def _ensure_currency_rates(self, currency):
        """Ensure currency rates are available for the selected currency"""
        try:
            # Check if we have recent currency rates (within last 7 days)
            from datetime import datetime, timedelta

            recent_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

            # Check if there's a recent rate for this currency
            recent_rate = request.env['res.currency.rate'].search([
                ('currency_id', '=', currency.id),
                ('name', '>=', recent_date)
            ], limit=1)

            if not recent_rate:
                # No recent rate found, try to update currency rates
                try:
                    # Check if ai_currencyrateupdate module is available
                    currency_update_model = request.env.get('currency.rate.update')
                    if currency_update_model:
                        # Create a currency update record and run the update
                        update_record = currency_update_model.create({})
                        update_record.update_currency_rates()

                        # Log the update attempt
                        import logging
                        _logger = logging.getLogger(__name__)
                        _logger.info(f"Triggered currency rate update for {currency.name}")
                    else:
                        # Fallback: create a basic rate if none exists
                        self._create_fallback_rate(currency)

                except Exception as e:
                    # If automatic update fails, create a fallback rate
                    import logging
                    _logger = logging.getLogger(__name__)
                    _logger.warning(f"Currency rate update failed for {currency.name}: {e}")
                    self._create_fallback_rate(currency)

        except Exception as e:
            # If anything fails, log but don't break the currency selection
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error ensuring currency rates for {currency.name}: {e}")

    def _create_fallback_rate(self, currency):
        """Create a fallback currency rate if none exists"""
        try:
            # Check if any rate exists for this currency
            existing_rate = request.env['res.currency.rate'].search([
                ('currency_id', '=', currency.id)
            ], limit=1)

            if not existing_rate:
                # No rate exists - this should be handled by the currency rate update module
                import logging
                _logger = logging.getLogger(__name__)
                _logger.warning(f"No currency rate found for {currency.name}. "
                              f"Please run the currency rate update to get current rates.")

                # Try to trigger currency rate update if the module is available
                try:
                    currency_update_model = request.env['currency.rate.update']
                    if currency_update_model:
                        _logger.info("Attempting to update currency rates automatically...")
                        # Create and run currency update
                        update_record = currency_update_model.create({})
                        update_record.update_currency_rates()
                except Exception as e:
                    _logger.error(f"Could not auto-update currency rates: {e}")

                # Don't create fallback rates - let the currency rate update module handle this
                # This ensures we always use accurate, up-to-date exchange rates

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error creating fallback rate for {currency.name}: {e}")

    @http.route('/test/popups', type='http', auth='public', website=True)
    def test_popups_page(self):
        """Test page for popups functionality"""
        return request.render('ai_amigoattiers.test_popups_page')

    # DISABLED - Currency test functionality removed
    # @http.route('/test/currency', type='http', auth='public', website=True)
    def test_currency_debug_disabled(self):
        """Debug currency functionality"""
        try:
            # Get current currency info
            current_currency = request.website.currency_id
            session_currency_id = request.session.get('website_sale_current_currency')
            cookie_currency = request.httprequest.cookies.get('selected_currency')

            # Get available currencies
            currencies = request.env['res.currency'].search([('active', '=', True)])

            debug_info = {
                'current_website_currency': {
                    'id': current_currency.id,
                    'name': current_currency.name,
                    'symbol': current_currency.symbol,
                },
                'session_currency_id': session_currency_id,
                'cookie_currency': cookie_currency,
                'available_currencies': [
                    {'id': c.id, 'name': c.name, 'symbol': c.symbol}
                    for c in currencies
                ],
                'session_data': dict(request.session),
                'cookies': dict(request.httprequest.cookies),
            }

            return f"<pre>{json.dumps(debug_info, indent=2)}</pre>"

        except Exception as e:
            return f"<pre>Error: {str(e)}</pre>"

    # DISABLED - Currency test functionality removed
    # @http.route('/test/currency-page', type='http', auth='public', website=True)
    def test_currency_page_disabled(self):
        """Test page for currency functionality"""
        return request.render('ai_amigoattiers.test_currency_page')

    # @http.route('/test/currency-simple', type='http', auth='public', website=True)
    def test_currency_simple_disabled(self):
        """Simple currency test"""
        try:
            # Force refresh the website currency
            website = request.website
            current_currency = website.currency_id

            # Get session currency
            session_currency_id = request.session.get('website_sale_current_currency')
            session_currency = None
            if session_currency_id:
                session_currency = request.env['res.currency'].browse(session_currency_id)

            # Get cookie currency
            cookie_currency = request.httprequest.cookies.get('selected_currency')

            # Get current pricelist
            current_pricelist = website.get_current_pricelist()

            # Get available pricelists
            all_pricelists = request.env['product.pricelist'].search([])

            # Test price conversion
            test_price_inr = 1000.0
            converted_price = test_price_inr
            conversion_rate = 1.0

            if current_currency.name != 'INR':
                # Get exchange rate
                inr_currency = request.env['res.currency'].search([('name', '=', 'INR')], limit=1)
                if inr_currency:
                    converted_price = inr_currency._convert(
                        test_price_inr, current_currency, website.company_id,
                        fields.Date.today()
                    )
                    # Calculate the conversion rate
                    if converted_price != test_price_inr:
                        conversion_rate = converted_price / test_price_inr

            # Get current currency rate
            current_rate = request.env['res.currency.rate'].search([
                ('currency_id', '=', current_currency.id)
            ], order='name desc', limit=1)

            result = f"""
            <h2>Currency & Pricelist Test Results</h2>
            <p><strong>Website Currency:</strong> {current_currency.name} ({current_currency.symbol})</p>
            <p><strong>Session Currency ID:</strong> {session_currency_id}</p>
            <p><strong>Session Currency:</strong> {session_currency.name if session_currency else 'None'}</p>
            <p><strong>Cookie Currency:</strong> {cookie_currency}</p>
            <p><strong>Current Pricelist:</strong> {current_pricelist.name} ({current_pricelist.currency_id.name})</p>
            <p><strong>Pricelist Currency Match:</strong> {'✅ Yes' if current_pricelist.currency_id == current_currency else '❌ No'}</p>
            <hr>
            <p><strong>Currency Rate Info:</strong></p>
            <p><strong>Current Rate:</strong> {current_rate.rate if current_rate else 'No rate found'} (Date: {current_rate.name if current_rate else 'N/A'})</p>
            <p><strong>Conversion Rate:</strong> 1 INR = {conversion_rate:.6f} {current_currency.name}</p>
            <hr>
            <p><strong>Price Test:</strong></p>
            <p>INR 1,000 = <span style="font-size: 20px; color: red;">{current_currency.symbol} {converted_price:.2f}</span></p>
            <hr>
            <h3>Available Pricelists:</h3>
            <ul>
            """

            for pricelist in all_pricelists:
                result += f"<li>{pricelist.name} - {pricelist.currency_id.name} ({pricelist.currency_id.symbol})</li>"

            result += """
            </ul>
            <hr>
            <h3>Actions:</h3>
            <p><a href="/test/update-currency-rates" class="btn btn-primary">Update Currency Rates</a></p>
            <p><a href="/test/currency-page" class="btn btn-secondary">Full Currency Test Page</a></p>
            """

            return result

        except Exception as e:
            return f"<pre>Error: {str(e)}</pre>"

    @http.route('/test/size-charts', type='http', auth='public', website=True)
    def test_size_charts(self):
        """Debug size chart functionality"""
        try:
            # Get all size charts
            size_charts = request.env['website.size.chart'].search([])

            # Get all products with size charts
            products_with_charts = request.env['product.template'].search([
                ('size_chart_id', '!=', False)
            ])

            # Get all products with gender
            products_with_gender = request.env['product.template'].search([
                ('gender', '!=', False)
            ])

            # Get all brands
            brands = request.env['product.brand'].search([])

            result = f"""
            <h2>Size Chart Debug Information</h2>
            <h3>Size Charts ({len(size_charts)} found)</h3>
            <ul>
            """

            for chart in size_charts:
                result += f"""
                <li><strong>{chart.name}</strong> - Gender: {chart.gender}, Brand: {chart.brand_id.name if chart.brand_id else 'None'}, Lines: {len(chart.size_line_ids)}</li>
                """

            result += f"""
            </ul>
            <h3>Products with Size Charts ({len(products_with_charts)} found)</h3>
            <ul>
            """

            for product in products_with_charts:
                result += f"""
                <li><strong>{product.name}</strong> - Chart: {product.size_chart_id.name}, Gender: {product.gender}, Brand: {product.brand_id.name if product.brand_id else 'None'}</li>
                """

            result += f"""
            </ul>
            <h3>Products with Gender ({len(products_with_gender)} found)</h3>
            <ul>
            """

            for product in products_with_gender:
                chart = product.get_size_chart()
                result += f"""
                <li><strong>{product.name}</strong> - Gender: {product.gender}, Auto-detected Chart: {chart.name if chart else 'None'}</li>
                """

            result += f"""
            </ul>
            <h3>Brands ({len(brands)} found)</h3>
            <ul>
            """

            for brand in brands:
                result += f"""
                <li><strong>{brand.name}</strong> - Active: {brand.active}</li>
                """

            result += """
            </ul>
            <hr>
            <h3>Actions:</h3>
            <p><a href="/test/popups" class="btn btn-primary">Test Popups Page</a></p>
            <p><a href="/shop" class="btn btn-secondary">Go to Shop</a></p>
            """

            return result

        except Exception as e:
            import traceback
            return f"<pre>Error: {str(e)}\n\nTraceback:\n{traceback.format_exc()}</pre>"

    # DISABLED - Currency rate update functionality removed
    # @http.route('/test/update-currency-rates', type='http', auth='public', website=True)
    def test_update_currency_rates_disabled(self):
        """Manually trigger currency rate update"""
        try:
            # Check if ai_currencyrateupdate module is available
            currency_update_model = request.env.get('currency.rate.update')
            if not currency_update_model:
                return "<h2>Error</h2><p>ai_currencyrateupdate module not found</p><a href='/test/currency-simple'>Back to Currency Test</a>"

            # Create a currency update record and run the update
            update_record = currency_update_model.create({})
            result = update_record.update_currency_rates()

            # Get the update result
            update_record_data = update_record.read(['success', 'message'])[0]

            status = "✅ Success" if update_record_data['success'] else "❌ Failed"
            message = update_record_data['message']

            return f"""
            <h2>Currency Rate Update Result</h2>
            <p><strong>Status:</strong> {status}</p>
            <p><strong>Message:</strong> {message}</p>
            <hr>
            <p><a href="/test/currency-simple" class="btn btn-primary">Check Currency Status</a></p>
            <p><a href="/test/currency-page" class="btn btn-secondary">Full Currency Test Page</a></p>
            """

        except Exception as e:
            return f"""
            <h2>Error</h2>
            <p>Failed to update currency rates: {str(e)}</p>
            <hr>
            <p><a href="/test/currency-simple" class="btn btn-primary">Back to Currency Test</a></p>
            """


# DISABLED - Currency handling from cookies removed
"""
class WebsiteMain(Website):
    \"\"\"Override main website controller to handle currency from cookies\"\"\"

    def _prepare_website_values(self, **kwargs):
        """Override to set currency from cookie"""
        values = super()._prepare_website_values(**kwargs)

        # Check for currency in cookies (avoid repeated processing)
        if not hasattr(request, '_currency_processed'):
            selected_currency = request.httprequest.cookies.get('selected_currency')

            # Ensure INR is always available and set as default if no currency is set
            try:
                inr_currency = request.env['res.currency'].search([('name', '=', 'INR')], limit=1)
                if not inr_currency:
                    # Create INR currency if it doesn't exist
                    inr_currency = request.env['res.currency'].create({
                        'name': 'INR',
                        'symbol': '₹',
                        'position': 'before',
                        'active': True,
                        'decimal_places': 2,
                    })
                    import logging
                    _logger = logging.getLogger(__name__)
                    _logger.info("Created INR currency as it was missing")

                # If no currency is selected or invalid currency, default to INR
                if not selected_currency or selected_currency == 'undefined' or selected_currency == 'null':
                    selected_currency = 'INR'
                    # Set INR as default in cookie
                    response = request.make_response('')
                    response.set_cookie('selected_currency', 'INR', max_age=86400, path='/')

                if selected_currency and selected_currency != 'INR':
                    # Find currency by code
                    currency = request.env['res.currency'].search([('name', '=', selected_currency)], limit=1)
                    if currency and currency.active:
                        # Set currency in session for consistency
                        request.session['website_sale_current_currency'] = currency.id

                        # Force update the website currency
                        request.website.currency_id = currency

                        # Update website in values to reflect the currency change
                        values['website'] = request.website

                        # Update pricelist to match currency
                        self._ensure_currency_pricelist(currency)

                        # Find and set the appropriate pricelist
                        matching_pricelist = request.env['product.pricelist'].search([
                            ('currency_id', '=', currency.id),
                            ('website_id', 'in', [False, request.website.id])
                        ], limit=1)
                        if matching_pricelist:
                            request.session['website_sale_current_pl'] = matching_pricelist.id
                    else:
                        # Invalid currency, fallback to INR
                        request.session['website_sale_current_currency'] = inr_currency.id
                        request.website.currency_id = inr_currency
                        values['website'] = request.website
                        self._ensure_currency_pricelist(inr_currency)
                else:
                    # Default to INR
                    request.session['website_sale_current_currency'] = inr_currency.id
                    request.website.currency_id = inr_currency
                    values['website'] = request.website
                    self._ensure_currency_pricelist(inr_currency)

            except Exception as e:
                # Log error but don't break the page - ensure INR fallback
                import logging
                _logger = logging.getLogger(__name__)
                _logger.error(f"Error setting currency from cookie: {e}")

                # Emergency fallback to INR
                try:
                    inr_currency = request.env['res.currency'].search([('name', '=', 'INR')], limit=1)
                    if inr_currency:
                        request.session['website_sale_current_currency'] = inr_currency.id
                        request.website.currency_id = inr_currency
                        values['website'] = request.website
                except:
                    pass  # Don't break the page even if this fails

            # Mark as processed to avoid repeated processing
            request._currency_processed = True

        return values
"""

