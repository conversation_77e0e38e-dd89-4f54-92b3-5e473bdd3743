# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request


class WebsiteSizeChartPricing(http.Controller):

    @http.route('/shop/size_chart', type='json', auth='public', website=True)
    def get_size_chart(self, product_id, **kwargs):
        """Get size chart data for a product"""
        try:
            product_id = int(product_id)
            product = request.env['product.template'].browse(product_id)
            
            if not product.exists():
                return {'error': 'Product not found'}
            
            # Get size chart for the product
            size_chart = product.get_size_chart()
            
            if not size_chart:
                return {'error': 'No size chart available for this product'}
            
            # Format size chart data for frontend
            size_chart_data = {
                'id': size_chart.id,
                'name': size_chart.name,
                'brand_name': size_chart.brand_id.name if size_chart.brand_id else '',
                'sizing_standard': size_chart.sizing_standard,
                'description': size_chart.description,
                'fitting_notes': size_chart.brand_id.fitting_notes if size_chart.brand_id else '',
                'sizes': []
            }
            
            # Add size lines
            for line in size_chart.size_line_ids:
                size_chart_data['sizes'].append({
                    'size_name': line.size_name,
                    'size_number': line.size_number,
                    'measurements': {
                        'chest': {'cm': line.chest_cm, 'inch': line.chest_inch},
                        'waist': {'cm': line.waist_cm, 'inch': line.waist_inch},
                        'hip': {'cm': line.hip_cm, 'inch': line.hip_inch},
                        'shoulder': {'cm': line.shoulder_cm, 'inch': line.shoulder_inch},
                        'sleeve': {'cm': line.sleeve_cm, 'inch': line.sleeve_inch},
                        'length': {'cm': line.length_cm, 'inch': line.length_inch}
                    }
                })
            
            return {'size_chart': size_chart_data}
            
        except Exception as e:
            return {'error': str(e)}

    # All country selection and currency functionality has been disabled
    # Only size chart functionality remains active




