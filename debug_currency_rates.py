#!/usr/bin/env python3
"""
Debug script to test the exact currency rate calculations
that should be happening in Odoo
"""

import requests

def debug_currency_calculation():
    print("=== DEBUGGING CURRENCY RATE CALCULATION ===")
    print()
    
    # Test with real API call
    try:
        api_key = "YOUR_API_KEY_HERE"  # Replace with actual key
        url = f"https://openexchangerates.org/api/latest.json?app_id={api_key}"
        
        print("Making API call to OpenExchangeRates...")
        # For testing, let's use mock data that matches current rates
        mock_response = {
            'base': 'USD',
            'rates': {
                'INR': 85.94,
                'EUR': 0.85,
                'GBP': 0.75,
                'CAD': 1.35,
                'AED': 3.67,
                'FJD': 2.25
            }
        }
        
        print("API Response (simulated with current rates):")
        for currency, rate in mock_response['rates'].items():
            print(f"  1 USD = {rate} {currency}")
        print()
        
        # Simulate Odoo calculation
        company_currency_code = 'INR'
        base_currency_code = 'USD'
        company_to_usd_rate = mock_response['rates'][company_currency_code]
        
        print(f"Company Currency: {company_currency_code}")
        print(f"Company to USD rate: 1 USD = {company_to_usd_rate} {company_currency_code}")
        print()
        
        print("=== ODOO RATE CALCULATIONS ===")
        
        currencies_in_odoo = ['USD', 'EUR', 'GBP', 'CAD', 'AED', 'FJD']
        
        for currency_code in currencies_in_odoo:
            if currency_code == company_currency_code:
                continue  # Skip INR (company currency)
                
            print(f"--- {currency_code} ---")
            
            if currency_code == base_currency_code:
                # USD rate
                odoo_rate = company_to_usd_rate
                print(f"Odoo rate: {odoo_rate}")
                print(f"Meaning: 1 {currency_code} = {odoo_rate} {company_currency_code}")
                
            elif currency_code in mock_response['rates']:
                # Other currencies
                api_rate = mock_response['rates'][currency_code]
                odoo_rate = company_to_usd_rate / api_rate
                print(f"API rate: 1 USD = {api_rate} {currency_code}")
                print(f"Calculation: {company_to_usd_rate} ÷ {api_rate} = {odoo_rate:.6f}")
                print(f"Odoo rate: {odoo_rate:.6f}")
                print(f"Meaning: 1 {currency_code} = {odoo_rate:.6f} {company_currency_code}")
            
            print()
        
        print("=== COMPARISON WITH YOUR CURRENT RATES ===")
        current_wrong_rates = {
            'USD': 1.432657,
            'EUR': 1.950664,  # Should be ~101.11
            'GBP': 1.950664,  # Should be ~114.59
            'CAD': 1.049157,  # Should be ~63.66
            'AED': 0.390047,  # Should be ~23.42
            'FJD': 0.636807   # Should be ~38.20
        }
        
        print("Current WRONG rates vs CORRECT rates:")
        for currency in ['USD', 'EUR', 'GBP', 'CAD', 'AED', 'FJD']:
            if currency in current_wrong_rates and currency in mock_response['rates']:
                wrong_rate = current_wrong_rates[currency]
                
                if currency == 'USD':
                    correct_rate = company_to_usd_rate
                else:
                    correct_rate = company_to_usd_rate / mock_response['rates'][currency]
                
                print(f"{currency}:")
                print(f"  ❌ Current: {wrong_rate}")
                print(f"  ✅ Should be: {correct_rate:.6f}")
                print(f"  📊 Difference: {abs(wrong_rate - correct_rate):.6f}")
                print()
        
        print("=== VERIFICATION: 889 INR to USD ===")
        inr_amount = 889
        
        # With wrong USD rate
        wrong_usd_rate = 1.432657
        wrong_conversion = inr_amount / wrong_usd_rate
        
        # With correct USD rate  
        correct_usd_rate = 85.94
        correct_conversion = inr_amount / correct_usd_rate
        
        print(f"₹{inr_amount} INR with WRONG rate ({wrong_usd_rate}): ${wrong_conversion:.2f} USD")
        print(f"₹{inr_amount} INR with CORRECT rate ({correct_usd_rate}): ${correct_conversion:.2f} USD")
        print()
        print("🚨 This confirms the USD rate is completely wrong!")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    debug_currency_calculation()
