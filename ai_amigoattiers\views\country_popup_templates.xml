<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Country Selection Popup Modal -->
    <template id="country_selection_modal" name="Country Selection Modal">
        <div class="modal fade" id="countrySelectionModal" tabindex="-1" role="dialog" aria-labelledby="countrySelectionModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-md" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="countrySelectionModalLabel">
                            <i class="fa fa-globe"></i> Select Your Country
                        </h5>
                    </div>
                    <div class="modal-body">
                        <p class="text-center mb-4">Please select your country to see prices in your local currency</p>
                        
                        <div class="country-grid" id="countryGrid">
                            <!-- Countries will be loaded dynamically via JavaScript -->
                            <div class="text-center">
                                <i class="fa fa-spinner fa-spin"></i> Loading countries...
                            </div>
                        </div>
                        
                        <!-- Default/Continue without selection -->
                        <div class="text-center mt-4">
                            <button type="button" class="btn btn-link text-muted" id="continueWithoutSelection">
                                Continue without selecting country
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- Country Selection Script Include -->
    <template id="country_selection_include" name="Country Selection Include" inherit_id="website.layout" priority="20">
        <xpath expr="//body" position="inside">
            <!-- Always include the modal, JavaScript will handle when to show it -->
            <t t-call="ai_amigoattiers.country_selection_modal"/>
        </xpath>
    </template>

    <!-- Current Country Display -->
    <template id="current_country_display" name="Current Country Display">
        <div class="current-country-display">
            <t t-set="selected_country" t-value="request.website.get_selected_country()"/>
            <t t-if="selected_country">
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="countryDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fa fa-globe"></i>
                        <t t-esc="selected_country.name"/>
                        <t t-if="selected_country.currency_id">
                            (<t t-esc="selected_country.currency_id.symbol"/>)
                        </t>
                    </button>
                    <div class="dropdown-menu" aria-labelledby="countryDropdown">
                        <a class="dropdown-item" href="#" data-toggle="modal" data-target="#countrySelectionModal">
                            <i class="fa fa-edit"></i> Change Country
                        </a>
                    </div>
                </div>
            </t>
            <t t-else="">
                <button class="btn btn-sm btn-outline-secondary" data-toggle="modal" data-target="#countrySelectionModal">
                    <i class="fa fa-globe"></i> Select Country
                </button>
            </t>
        </div>
    </template>


</odoo>
