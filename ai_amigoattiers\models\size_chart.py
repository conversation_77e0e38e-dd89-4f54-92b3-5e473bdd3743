# -*- coding: utf-8 -*-

from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)


class ProductBrand(models.Model):
    _name = 'product.brand'
    _description = 'Product Brand'

    name = fields.Char('Brand Name', required=True)
    description = fields.Text('Description')
    logo = fields.Binary('Logo')
    active = fields.Boolean('Active', default=True)

    # Brand-specific fitting notes
    fitting_notes = fields.Html('Fitting Notes', help='Brand-specific fitting information')


class SizeChartHeader(models.Model):
    _name = 'website.size.chart.header'
    _description = 'Size Chart Header Configuration'
    _order = 'sequence'

    chart_id = fields.Many2one('website.size.chart', string='Size Chart', required=True, ondelete='cascade')
    sequence = fields.Integer('Sequence', default=10)

    # Header configuration
    header_name = fields.Char('Header Name', required=True)  # e.g., 'Chest', 'Waist', 'Hip', etc.
    header_key = fields.Char('Header Key', required=True)  # e.g., 'chest', 'waist', 'hip' (used for data mapping)
    unit = fields.Selection([
        ('cm', 'Centimeters'),
        ('inch', 'Inches'),
        ('size', 'Size Only'),
        ('text', 'Text')
    ], string='Unit Type', default='cm')
    is_required = fields.Boolean('Required', default=False)
    help_text = fields.Char('Help Text')
    active = fields.Boolean('Active', default=True)


class SizeChart(models.Model):
    _name = 'website.size.chart'
    _description = 'Size Chart for Products'
    _order = 'gender, brand_id, sequence'

    name = fields.Char('Chart Name', required=True)
    gender = fields.Selection([
        ('male', 'Male'),
        ('female', 'Female'),
        ('unisex', 'Unisex')
    ], string='Gender', required=True, default='male')

    brand_id = fields.Many2one('product.brand', string='Brand', help='Brand-specific fitting variations')
    category_id = fields.Many2one('product.category', string='Product Category')

    sequence = fields.Integer('Sequence', default=10)
    active = fields.Boolean('Active', default=True)

    # Size chart configuration
    header_ids = fields.One2many('website.size.chart.header', 'chart_id', string='Chart Headers')
    size_line_ids = fields.One2many('website.size.chart.line', 'chart_id', string='Size Lines')

    # Indian sizing standards
    sizing_standard = fields.Selection([
        ('indian', 'Indian Standard'),
        ('international', 'International'),
        ('brand_specific', 'Brand Specific')
    ], string='Sizing Standard', default='indian')

    description = fields.Html('Description')

    @api.model
    def create(self, vals):
        """Create default headers if none provided"""
        chart = super().create(vals)
        if not chart.header_ids:
            chart._create_default_headers()
        return chart

    def _create_default_headers(self):
        """Create default headers based on category"""
        # Get category-specific headers
        headers = self._get_category_headers()

        for header_data in headers:
            header_data['chart_id'] = self.id
            self.env['website.size.chart.header'].create(header_data)

    def _get_category_headers(self):
        """Get headers based on product category"""
        # Default headers for general products
        default_headers = [
            {'header_name': 'Size', 'header_key': 'size_name', 'unit': 'size', 'sequence': 1, 'is_required': True},
            {'header_name': 'Size Number', 'header_key': 'size_number', 'unit': 'text', 'sequence': 2},
            {'header_name': 'Chest', 'header_key': 'chest', 'unit': 'cm', 'sequence': 3},
            {'header_name': 'Waist', 'header_key': 'waist', 'unit': 'cm', 'sequence': 4},
            {'header_name': 'Hip', 'header_key': 'hip', 'unit': 'cm', 'sequence': 5},
            {'header_name': 'Shoulder', 'header_key': 'shoulder', 'unit': 'cm', 'sequence': 6},
            {'header_name': 'Sleeve', 'header_key': 'sleeve', 'unit': 'cm', 'sequence': 7},
            {'header_name': 'Length', 'header_key': 'length', 'unit': 'cm', 'sequence': 8},
        ]

        if not self.category_id:
            return default_headers

        category_name = self.category_id.name.lower()

        # Category-specific headers
        if 'kurta' in category_name or 'top' in category_name or 'shirt' in category_name:
            # For tops/kurtas
            return [
                {'header_name': 'Size', 'header_key': 'size_name', 'unit': 'size', 'sequence': 1, 'is_required': True},
                {'header_name': 'Size Number', 'header_key': 'size_number', 'unit': 'text', 'sequence': 2},
                {'header_name': 'Chest', 'header_key': 'chest', 'unit': 'cm', 'sequence': 3},
                {'header_name': 'Waist', 'header_key': 'waist', 'unit': 'cm', 'sequence': 4},
                {'header_name': 'Shoulder', 'header_key': 'shoulder', 'unit': 'cm', 'sequence': 5},
                {'header_name': 'Sleeve', 'header_key': 'sleeve', 'unit': 'cm', 'sequence': 6},
                {'header_name': 'Length', 'header_key': 'length', 'unit': 'cm', 'sequence': 7},
            ]
        elif 'bottom' in category_name or 'pant' in category_name or 'trouser' in category_name:
            # For bottoms/pants
            return [
                {'header_name': 'Size', 'header_key': 'size_name', 'unit': 'size', 'sequence': 1, 'is_required': True},
                {'header_name': 'Size Number', 'header_key': 'size_number', 'unit': 'text', 'sequence': 2},
                {'header_name': 'Waist', 'header_key': 'waist', 'unit': 'cm', 'sequence': 3},
                {'header_name': 'Hip', 'header_key': 'hip', 'unit': 'cm', 'sequence': 4},
                {'header_name': 'Inseam', 'header_key': 'inseam', 'unit': 'cm', 'sequence': 5},
                {'header_name': 'Length', 'header_key': 'length', 'unit': 'cm', 'sequence': 6},
            ]
        elif 'dress' in category_name or 'gown' in category_name:
            # For dresses
            return [
                {'header_name': 'Size', 'header_key': 'size_name', 'unit': 'size', 'sequence': 1, 'is_required': True},
                {'header_name': 'Size Number', 'header_key': 'size_number', 'unit': 'text', 'sequence': 2},
                {'header_name': 'Bust', 'header_key': 'chest', 'unit': 'cm', 'sequence': 3},
                {'header_name': 'Waist', 'header_key': 'waist', 'unit': 'cm', 'sequence': 4},
                {'header_name': 'Hip', 'header_key': 'hip', 'unit': 'cm', 'sequence': 5},
                {'header_name': 'Shoulder', 'header_key': 'shoulder', 'unit': 'cm', 'sequence': 6},
                {'header_name': 'Length', 'header_key': 'length', 'unit': 'cm', 'sequence': 7},
            ]
        else:
            # Default headers for other categories
            return default_headers
    
    @api.model
    def get_size_chart(self, product_id, gender=None):
        """Get appropriate size chart for a product"""
        product = self.env['product.template'].browse(product_id)
        
        domain = [('active', '=', True)]
        
        # Filter by gender if provided
        if gender:
            domain.append(('gender', 'in', [gender, 'unisex']))
        
        # Filter by brand if product has brand
        if hasattr(product, 'brand_id') and product.brand_id:
            domain.append(('brand_id', 'in', [product.brand_id.id, False]))
        
        # Filter by category
        if product.categ_id:
            domain.append(('category_id', 'in', [product.categ_id.id, False]))
        
        # Get the most specific chart (brand-specific first, then general)
        charts = self.search(domain, order='brand_id desc, sequence')
        
        return charts[0] if charts else False


class SizeChartLine(models.Model):
    _name = 'website.size.chart.line'
    _description = 'Size Chart Line'
    _order = 'sequence'

    chart_id = fields.Many2one('website.size.chart', string='Size Chart', required=True, ondelete='cascade')
    sequence = fields.Integer('Sequence', default=10)

    # Size information
    size_name = fields.Char('Size', required=True)  # XS, S, M, L, XL, XXL
    size_number = fields.Char('Size Number')  # 28, 30, 32, 34, 36, 38, 40, 42

    # Dynamic measurements - stored as JSON for flexibility
    measurements = fields.Json('Measurements', default=dict)

    # Legacy fields for backward compatibility (will be migrated to measurements JSON)
    chest_cm = fields.Float('Chest (cm)')
    waist_cm = fields.Float('Waist (cm)')
    hip_cm = fields.Float('Hip (cm)')
    shoulder_cm = fields.Float('Shoulder (cm)')
    sleeve_cm = fields.Float('Sleeve (cm)')
    length_cm = fields.Float('Length (cm)')

    @api.model
    def create(self, vals):
        """Migrate legacy fields to measurements JSON on create"""
        line = super().create(vals)
        line._migrate_legacy_measurements()
        return line

    def write(self, vals):
        """Migrate legacy fields to measurements JSON on write"""
        result = super().write(vals)
        self._migrate_legacy_measurements()
        return result

    def _migrate_legacy_measurements(self):
        """Migrate legacy measurement fields to JSON format"""
        for line in self:
            if not line.measurements:
                line.measurements = {}

            # Migrate legacy fields to measurements JSON
            legacy_mapping = {
                'chest': line.chest_cm,
                'waist': line.waist_cm,
                'hip': line.hip_cm,
                'shoulder': line.shoulder_cm,
                'sleeve': line.sleeve_cm,
                'length': line.length_cm,
            }

            for key, value in legacy_mapping.items():
                if value and (key not in line.measurements or not line.measurements[key]):
                    line.measurements[key] = value

    def get_measurement(self, header_key):
        """Get measurement value for a specific header"""
        if header_key == 'size_name':
            return self.size_name
        elif header_key == 'size_number':
            return self.size_number
        elif self.measurements and header_key in self.measurements:
            return self.measurements[header_key]
        else:
            # Fallback to legacy fields
            legacy_mapping = {
                'chest': self.chest_cm,
                'waist': self.waist_cm,
                'hip': self.hip_cm,
                'shoulder': self.shoulder_cm,
                'sleeve': self.sleeve_cm,
                'length': self.length_cm,
            }
            return legacy_mapping.get(header_key, '')

    def set_measurement(self, header_key, value):
        """Set measurement value for a specific header"""
        if not self.measurements:
            self.measurements = {}
        self.measurements[header_key] = value
    
    # Additional measurements for specific garments
    neck_cm = fields.Float('Neck (cm)')
    inseam_cm = fields.Float('Inseam (cm)')
    
    # Measurements in inches (for display)
    chest_inch = fields.Float('Chest (inch)', compute='_compute_inches', store=True)
    waist_inch = fields.Float('Waist (inch)', compute='_compute_inches', store=True)
    hip_inch = fields.Float('Hip (inch)', compute='_compute_inches', store=True)
    shoulder_inch = fields.Float('Shoulder (inch)', compute='_compute_inches', store=True)
    sleeve_inch = fields.Float('Sleeve (inch)', compute='_compute_inches', store=True)
    length_inch = fields.Float('Length (inch)', compute='_compute_inches', store=True)
    neck_inch = fields.Float('Neck (inch)', compute='_compute_inches', store=True)
    inseam_inch = fields.Float('Inseam (inch)', compute='_compute_inches', store=True)
    
    @api.depends('chest_cm', 'waist_cm', 'hip_cm', 'shoulder_cm', 'sleeve_cm', 'length_cm', 'neck_cm', 'inseam_cm')
    def _compute_inches(self):
        """Convert centimeters to inches"""
        for line in self:
            line.chest_inch = line.chest_cm / 2.54 if line.chest_cm else 0
            line.waist_inch = line.waist_cm / 2.54 if line.waist_cm else 0
            line.hip_inch = line.hip_cm / 2.54 if line.hip_cm else 0
            line.shoulder_inch = line.shoulder_cm / 2.54 if line.shoulder_cm else 0
            line.sleeve_inch = line.sleeve_cm / 2.54 if line.sleeve_cm else 0
            line.length_inch = line.length_cm / 2.54 if line.length_cm else 0
            line.neck_inch = line.neck_cm / 2.54 if line.neck_cm else 0
            line.inseam_inch = line.inseam_cm / 2.54 if line.inseam_cm else 0


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    size_chart_id = fields.Many2one('website.size.chart', string='Size Chart')
    brand_id = fields.Many2one('product.brand', string='Brand')
    gender = fields.Selection([
        ('male', 'Male'),
        ('female', 'Female'),
        ('unisex', 'Unisex')
    ], string='Gender')

    def get_size_chart(self):
        """Get size chart for this product"""
        if self.size_chart_id:
            return self.size_chart_id

        # Auto-detect based on gender and brand
        size_chart_obj = self.env['website.size.chart']
        return size_chart_obj.get_size_chart(self.id, self.gender)


