<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Size Chart Modal Template -->
    <template id="size_chart_modal" name="Size Chart Modal">
        <div class="modal fade" id="sizeChartModal" tabindex="-1" role="dialog" aria-labelledby="sizeChartModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="sizeChartModalLabel">
                            <i class="fa fa-ruler"></i> Size Chart
                            <span t-if="size_chart.brand_id" class="text-muted">- <t t-esc="size_chart.brand_id.name"/></span>
                        </h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <!-- Unit Toggle -->
                        <div class="unit-toggle text-center mb-4">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary active" data-unit="cm">
                                    <i class="fa fa-arrows-h unit-icon"></i> cm
                                </button>
                                <button type="button" class="btn btn-outline-primary" data-unit="inch">
                                    <i class="fa fa-arrows-h unit-icon"></i> inch
                                </button>
                            </div>
                        </div>
                        
                        <!-- Size Chart Table -->
                        <div class="size-chart-container">
                            <div class="table-responsive">
                                <table class="table table-bordered size-chart-table">
                                    <thead class="thead-light">
                                        <tr>
                                            <!-- Dynamic headers based on size chart configuration -->
                                            <t t-foreach="size_chart.header_ids.filtered(lambda h: h.active)" t-as="header">
                                                <th t-att-class="'measurement-col' if header.unit == 'cm' else ''"
                                                    t-att-data-measurement="header.header_key">
                                                    <t t-esc="header.header_name"/>
                                                </th>
                                            </t>
                                            <!-- Fallback to default headers if no custom headers are defined -->
                                            <t t-if="not size_chart.header_ids">
                                                <th>Size</th>
                                                <th t-if="any(line.size_number for line in size_chart.size_line_ids)">Size Number</th>
                                                <th class="measurement-col" data-measurement="chest">Chest</th>
                                                <th class="measurement-col" data-measurement="waist">Waist</th>
                                                <th class="measurement-col" data-measurement="hip">Hip</th>
                                                <th class="measurement-col" data-measurement="shoulder">Shoulder</th>
                                                <th class="measurement-col" data-measurement="sleeve">Sleeve</th>
                                                <th class="measurement-col" data-measurement="length">Length</th>
                                            </t>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr t-foreach="size_chart.size_line_ids" t-as="line">
                                            <!-- Dynamic data based on headers -->
                                            <t t-foreach="size_chart.header_ids.filtered(lambda h: h.active)" t-as="header">
                                                <t t-set="measurement_value" t-value="line.get_measurement(header.header_key)"/>
                                                <t t-if="header.unit == 'cm'">
                                                    <!-- Measurement column with cm/inch conversion -->
                                                    <td class="measurement-value"
                                                        t-att-data-cm="measurement_value"
                                                        t-att-data-inch="'%.1f' % (measurement_value / 2.54) if measurement_value else ''">
                                                        <span class="cm-value"><t t-esc="'%.0f' % measurement_value if measurement_value else '-'"/></span>
                                                        <span class="inch-value d-none"><t t-esc="'%.1f' % (measurement_value / 2.54) if measurement_value else '-'"/></span>
                                                    </td>
                                                </t>
                                                <t t-elif="header.unit == 'size'">
                                                    <!-- Size column (bold) -->
                                                    <td class="font-weight-bold"><t t-esc="measurement_value or '-'"/></td>
                                                </t>
                                                <t t-else="">
                                                    <!-- Text column -->
                                                    <td><t t-esc="measurement_value or '-'"/></td>
                                                </t>
                                            </t>

                                            <!-- Fallback to default data if no custom headers -->
                                            <t t-if="not size_chart.header_ids">
                                                <td class="font-weight-bold"><t t-esc="line.size_name"/></td>
                                                <td t-if="any(l.size_number for l in size_chart.size_line_ids)">
                                                    <t t-esc="line.size_number"/>
                                                </td>
                                                <td class="measurement-value"
                                                    t-att-data-cm="line.chest_cm"
                                                    t-att-data-inch="'%.1f' % line.chest_inch if line.chest_inch else ''">
                                                    <span class="cm-value"><t t-esc="'%.0f' % line.chest_cm if line.chest_cm else '-'"/></span>
                                                    <span class="inch-value d-none"><t t-esc="'%.1f' % line.chest_inch if line.chest_inch else '-'"/></span>
                                                </td>
                                                <td class="measurement-value"
                                                    t-att-data-cm="line.waist_cm"
                                                    t-att-data-inch="'%.1f' % line.waist_inch if line.waist_inch else ''">
                                                    <span class="cm-value"><t t-esc="'%.0f' % line.waist_cm if line.waist_cm else '-'"/></span>
                                                    <span class="inch-value d-none"><t t-esc="'%.1f' % line.waist_inch if line.waist_inch else '-'"/></span>
                                                </td>
                                                <td class="measurement-value"
                                                    t-att-data-cm="line.hip_cm"
                                                    t-att-data-inch="'%.1f' % line.hip_inch if line.hip_inch else ''">
                                                    <span class="cm-value"><t t-esc="'%.0f' % line.hip_cm if line.hip_cm else '-'"/></span>
                                                    <span class="inch-value d-none"><t t-esc="'%.1f' % line.hip_inch if line.hip_inch else '-'"/></span>
                                                </td>
                                                <td class="measurement-value"
                                                    t-att-data-cm="line.shoulder_cm"
                                                    t-att-data-inch="'%.1f' % line.shoulder_inch if line.shoulder_inch else ''">
                                                    <span class="cm-value"><t t-esc="'%.0f' % line.shoulder_cm if line.shoulder_cm else '-'"/></span>
                                                    <span class="inch-value d-none"><t t-esc="'%.1f' % line.shoulder_inch if line.shoulder_inch else '-'"/></span>
                                                </td>
                                                <td class="measurement-value"
                                                    t-att-data-cm="line.sleeve_cm"
                                                    t-att-data-inch="'%.1f' % line.sleeve_inch if line.sleeve_inch else ''">
                                                    <span class="cm-value"><t t-esc="'%.0f' % line.sleeve_cm if line.sleeve_cm else '-'"/></span>
                                                    <span class="inch-value d-none"><t t-esc="'%.1f' % line.sleeve_inch if line.sleeve_inch else '-'"/></span>
                                                </td>
                                                <td class="measurement-value"
                                                    t-att-data-cm="line.length_cm"
                                                    t-att-data-inch="'%.1f' % line.length_inch if line.length_inch else ''">
                                                    <span class="cm-value"><t t-esc="'%.0f' % line.length_cm if line.length_cm else '-'"/></span>
                                                    <span class="inch-value d-none"><t t-esc="'%.1f' % line.length_inch if line.length_inch else '-'"/></span>
                                                </td>
                                            </t>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- Brand Fitting Notes -->
                        <div t-if="size_chart.brand_id and size_chart.brand_id.fitting_notes" class="mt-3">
                            <h6><i class="fa fa-info-circle"></i> Brand Fitting Notes</h6>
                            <div class="alert alert-info">
                                <t t-raw="size_chart.brand_id.fitting_notes"/>
                            </div>
                        </div>
                        
                        <!-- General Description -->
                        <div t-if="size_chart.description" class="mt-3">
                            <h6><i class="fa fa-info-circle"></i> Size Guide</h6>
                            <div class="size-guide-description">
                                <t t-raw="size_chart.description"/>
                            </div>
                        </div>
                        
                        <!-- Sizing Standard Info -->
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fa fa-tag"></i> 
                                Sizing Standard: 
                                <span t-if="size_chart.sizing_standard == 'indian'">Indian Standard</span>
                                <span t-elif="size_chart.sizing_standard == 'international'">International</span>
                                <span t-else="">Brand Specific</span>
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- Size Chart Button for Product Pages -->
    <template id="product_size_chart_button" name="Size Chart Button" inherit_id="website_sale.product" priority="20">
        <xpath expr="//div[@id='product_details']" position="inside">
            <!-- DEBUG INFO - COMMENTED OUT FOR PRODUCTION -->
            <!--
            <div class="mt-3 p-2 bg-light border" style="font-size: 12px;">
                <strong>Size Chart Debug:</strong><br/>
                Product ID: <span t-esc="product.id"/><br/>
                Product Name: <span t-esc="product.name"/><br/>
                Gender: <span t-esc="product.gender"/><br/>
                Brand: <span t-esc="product.brand_id.name if product.brand_id else 'None'"/><br/>
                Size Chart ID: <span t-esc="product.size_chart_id.id if product.size_chart_id else 'None'"/><br/>
                Get Size Chart Result: <span t-esc="product.get_size_chart().name if product.get_size_chart() else 'None'"/>
            </div>
            -->

            <!-- TESTING BUTTONS - COMMENTED OUT FOR PRODUCTION -->
            <!--
            <div class="mt-3">
                <button type="button" class="btn btn-outline-info btn-sm"
                        data-toggle="modal"
                        data-target="#sizeChartModal"
                        t-attf-data-product-id="#{product.id}">
                    <i class="fa fa-ruler"></i> Size Chart (Always Show)
                </button>
            </div>

            <div class="mt-2">
                <button type="button" class="btn btn-warning btn-sm" t-attf-onclick="alert('Test button clicked! Product ID: #{product.id}')">
                    🔧 Debug Test Button
                </button>
            </div>
            -->

            <!-- PRODUCTION SIZE CHART BUTTON -->
            <div t-if="product.get_size_chart()" class="mt-3">
                <button type="button" class="btn btn-outline-info btn-sm"
                        data-toggle="modal"
                        data-target="#sizeChartModal"
                        t-attf-data-product-id="#{product.id}">
                    <i class="fa fa-ruler"></i> Size Chart
                </button>
            </div>
        </xpath>
    </template>

    <!-- Include Size Chart Modal in Product Pages -->
    <template id="product_size_chart_modal_include" name="Include Size Chart Modal" inherit_id="website_sale.product" priority="30">
        <xpath expr="//div[@id='wrap']" position="after">
            <t t-set="size_chart" t-value="product.get_size_chart()"/>
            <t t-if="size_chart">
                <t t-call="ai_amigoattiers.size_chart_modal"/>

                <!-- Inline JavaScript for Size Chart -->
                <script type="text/javascript">
                    // Immediate jQuery check - if not available, use vanilla JavaScript
                    var useSizeChartVanillaJS = (typeof $ === 'undefined');

                    // Wait for both DOM and jQuery to be ready
                    function initSizeChart() {
                        // Prevent multiple initializations
                        if (window.sizeChartInitialized) {
                            // console.log('Size chart already initialized, skipping...');
                            return;
                        }

                        // Check if jQuery is available
                        if (typeof $ === 'undefined') {
                            // console.log('jQuery not available for size chart, retrying in 200ms...');
                            // Try up to 10 times (2 seconds) then fallback
                            if (!window.jquerySizeChartRetryCount) window.jquerySizeChartRetryCount = 0;
                            if (window.jquerySizeChartRetryCount &lt; 10) {
                                window.jquerySizeChartRetryCount++;
                                setTimeout(initSizeChart, 200);
                                return;
                            } else {
                                // console.error('jQuery failed to load for size chart after 2 seconds, using vanilla JavaScript fallback');
                                initSizeChartVanilla();
                                return;
                            }
                        }

                        // Mark as initialized
                        window.sizeChartInitialized = true;

                        $(document).ready(function() {
                        // Size Chart Functionality
                        // console.log('Initializing size chart...');

                        // DEBUG: Check if modal exists - COMMENTED FOR PRODUCTION
                        // var modal = document.getElementById('sizeChartModal');
                        // console.log('Size chart modal found:', modal ? 'YES' : 'NO');
                        // if (modal) {
                        //     console.log('Modal classes:', modal.className);
                        // }

                        var currentUnit = 'cm'; // Default to centimeters (Indian standard)

                        // Handle size chart button clicks (remove existing handlers first)
                        $('[data-toggle="modal"][data-target="#sizeChartModal"]').off('click').on('click', function(e) {
                            e.preventDefault();
                            // console.log('Size chart button clicked');

                            // Simply show the modal - data is already rendered in template
                            $('#sizeChartModal').modal('show');
                        });

                        // Handle modal show event (remove existing handlers first)
                        $('#sizeChartModal').off('show.bs.modal').on('show.bs.modal', function() {
                            // console.log('Size chart modal opening...');

                            // Track analytics if needed
                            if (typeof gtag !== 'undefined') {
                                gtag('event', 'size_chart_opened', {
                                    'event_category': 'engagement',
                                    'event_label': 'size_chart'
                                });
                            }

                            // Ensure proper unit is displayed
                            toggleMeasurementUnit(currentUnit);
                        });

                        // Handle unit toggle (remove existing handlers first)
                        $('#sizeChartModal').off('click', '.unit-toggle .btn').on('click', '.unit-toggle .btn', function(e) {
                            e.preventDefault();
                            var unit = $(this).data('unit');

                            // Update button states
                            $(this).addClass('active').siblings().removeClass('active');

                            // Toggle measurements
                            toggleMeasurementUnit(unit);
                            currentUnit = unit;

                            // Add animation effect
                            animateUnitChange();
                        });

                        // Handle close button clicks
                        $('#sizeChartModal').off('click', '[data-dismiss="modal"], [data-bs-dismiss="modal"]').on('click', '[data-dismiss="modal"], [data-bs-dismiss="modal"]', function(e) {
                            // console.log('Close button clicked');
                            $('#sizeChartModal').modal('hide');
                        });

                        // Handle modal hidden event (remove existing handlers first)
                        $('#sizeChartModal').off('hidden.bs.modal').on('hidden.bs.modal', function() {
                            // Reset to default unit
                            currentUnit = 'cm';
                            $('.unit-toggle .btn[data-unit="cm"]').addClass('active');
                            $('.unit-toggle .btn[data-unit="inch"]').removeClass('active');
                        });

                        function loadSizeChartData(productId) {
                            console.log('Loading size chart for product:', productId);

                            // Add loading overlay
                            var $modal = $('#sizeChartModal');
                            $modal.find('.modal-body').append('&lt;div class="loading-overlay text-center"&gt;&lt;i class="fa fa-spinner fa-spin"&gt;&lt;/i&gt; Loading...&lt;/div&gt;');

                            $.ajax({
                                url: '/shop/size_chart',
                                type: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    jsonrpc: '2.0',
                                    method: 'call',
                                    params: {
                                        product_id: productId
                                    }
                                }),
                                success: function(data) {
                                    // Remove loading state
                                    $modal.find('.loading-overlay').remove();

                                    if (data &amp;&amp; data.error) {
                                        showSizeChartError(data.error);
                                    } else if (data &amp;&amp; data.size_chart) {
                                        console.log('Size chart loaded successfully');
                                        // Size chart is already rendered in template
                                    } else {
                                        showSizeChartError('No size chart available for this product.');
                                    }
                                },
                                error: function(xhr, status, error) {
                                    // Remove loading state
                                    $modal.find('.loading-overlay').remove();
                                    showSizeChartError('Failed to load size chart. Please try again.');
                                    console.error('Size chart loading error:', error);
                                }
                            });
                        }

                        function toggleMeasurementUnit(unit) {
                            if (unit === 'cm') {
                                $('.cm-value').removeClass('d-none');
                                $('.inch-value').addClass('d-none');
                            } else {
                                $('.cm-value').addClass('d-none');
                                $('.inch-value').removeClass('d-none');
                            }
                        }

                        function animateUnitChange() {
                            var $table = $('.size-chart-table tbody');

                            $table.addClass('changing-units');

                            setTimeout(function() {
                                $table.removeClass('changing-units');
                            }, 300);
                        }

                        function showSizeChartError(message) {
                            var $modal = $('#sizeChartModal');
                            var errorHtml = '&lt;div class="alert alert-danger text-center"&gt;' +
                                           '&lt;i class="fa fa-exclamation-triangle"&gt;&lt;/i&gt; ' + message +
                                           '&lt;/div&gt;';

                            $modal.find('.modal-body').html(errorHtml);
                        }

                        // Add CSS for animations
                        $('&lt;style&gt;')
                            .prop('type', 'text/css')
                            .html('.changing-units { opacity: 0.7; transition: opacity 0.3s ease; } ' +
                                  '.measurement-col { transition: all 0.3s ease; } ' +
                                  '.loading-overlay { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 1000; background: rgba(255,255,255,0.9); padding: 20px; border-radius: 5px; }')
                            .appendTo('head');
                        });
                    }

                    // Vanilla JavaScript fallback for when jQuery is not available
                    function initSizeChartVanilla() {
                        // Prevent multiple initializations
                        if (window.sizeChartVanillaInitialized) {
                            // console.log('Size chart vanilla already initialized, skipping...');
                            return;
                        }
                        window.sizeChartVanillaInitialized = true;

                        // console.log('Initializing size chart with vanilla JavaScript...');

                        var currentUnit = 'cm';

                        // Handle size chart button clicks
                        var sizeChartButtons = document.querySelectorAll('[data-toggle="modal"][data-target="#sizeChartModal"]');
                        sizeChartButtons.forEach(function(button) {
                            button.addEventListener('click', function(e) {
                                e.preventDefault();
                                // console.log('Size chart button clicked (vanilla JS)');

                                // Show modal using available method
                                var modal = document.getElementById('sizeChartModal');
                                if (modal) {
                                    // Try Bootstrap 5 first
                                    if (typeof bootstrap !== 'undefined' &amp;&amp; bootstrap.Modal) {
                                        var bsModal = new bootstrap.Modal(modal);
                                        bsModal.show();
                                    }
                                    // Try Bootstrap 4 with jQuery
                                    else if (typeof $ !== 'undefined' &amp;&amp; $.fn.modal) {
                                        $(modal).modal('show');
                                    }
                                    // Vanilla fallback
                                    else {
                                        modal.style.display = 'block';
                                        modal.classList.add('show');
                                        document.body.classList.add('modal-open');
                                        // Add backdrop
                                        var backdrop = document.createElement('div');
                                        backdrop.className = 'modal-backdrop fade show';
                                        backdrop.id = 'sizeChartBackdrop';
                                        document.body.appendChild(backdrop);
                                    }
                                }
                            });
                        });

                        // Handle close button clicks
                        var closeButtons = document.querySelectorAll('#sizeChartModal [data-dismiss="modal"], #sizeChartModal [data-bs-dismiss="modal"]');
                        closeButtons.forEach(function(button) {
                            button.addEventListener('click', function(e) {
                                e.preventDefault();
                                // console.log('Close button clicked (vanilla JS)');

                                var modal = document.getElementById('sizeChartModal');
                                if (modal) {
                                    // Try Bootstrap 5 first
                                    if (typeof bootstrap !== 'undefined' &amp;&amp; bootstrap.Modal) {
                                        var bsModal = bootstrap.Modal.getInstance(modal);
                                        if (bsModal) {
                                            bsModal.hide();
                                        }
                                    }
                                    // Try Bootstrap 4 with jQuery
                                    else if (typeof $ !== 'undefined' &amp;&amp; $.fn.modal) {
                                        $(modal).modal('hide');
                                    }
                                    // Vanilla fallback
                                    else {
                                        modal.style.display = 'none';
                                        modal.classList.remove('show');
                                        document.body.classList.remove('modal-open');
                                        // Remove backdrop
                                        var backdrop = document.getElementById('sizeChartBackdrop');
                                        if (backdrop) {
                                            backdrop.remove();
                                        }
                                    }
                                }
                            });
                        });

                        // Handle unit toggle
                        var unitButtons = document.querySelectorAll('#sizeChartModal .unit-toggle .btn');
                        unitButtons.forEach(function(button) {
                            button.addEventListener('click', function(e) {
                                e.preventDefault();
                                var unit = this.getAttribute('data-unit');

                                // Update button states
                                unitButtons.forEach(function(btn) {
                                    btn.classList.remove('active');
                                });
                                this.classList.add('active');

                                // Toggle measurements
                                toggleMeasurementUnitVanilla(unit);
                                currentUnit = unit;
                            });
                        });

                        function toggleMeasurementUnitVanilla(unit) {
                            var cmValues = document.querySelectorAll('.cm-value');
                            var inchValues = document.querySelectorAll('.inch-value');

                            if (unit === 'cm') {
                                cmValues.forEach(function(el) { el.classList.remove('d-none'); });
                                inchValues.forEach(function(el) { el.classList.add('d-none'); });
                            } else {
                                cmValues.forEach(function(el) { el.classList.add('d-none'); });
                                inchValues.forEach(function(el) { el.classList.remove('d-none'); });
                            }
                        }
                    }

                    // Start initialization when DOM is ready
                    if (document.readyState === 'loading') {
                        document.addEventListener('DOMContentLoaded', function() {
                            if (useSizeChartVanillaJS || typeof $ === 'undefined') {
                                // console.log('Using vanilla JavaScript for size chart');
                                initSizeChartVanilla();
                            } else {
                                initSizeChart();
                            }
                        });
                    } else {
                        if (useSizeChartVanillaJS || typeof $ === 'undefined') {
                            // console.log('Using vanilla JavaScript for size chart (immediate)');
                            initSizeChartVanilla();
                        } else {
                            initSizeChart();
                        }
                    }
                </script>
            </t>
        </xpath>
    </template>
</odoo>

