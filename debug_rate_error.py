#!/usr/bin/env python3
"""
Debug why the rates are too small
"""

def debug_rate_calculation():
    print("=== DEBUGGING RATE CALCULATION ERROR ===")
    print()
    
    # Current wrong results
    current_usd_rate = 0.000194
    current_aed_rate = 0.000713
    
    # Expected results
    expected_usd_rate = 0.011636025  # 1/85.94
    expected_aed_rate = 0.042704212  # 3.67/85.94
    
    print("Current vs Expected:")
    print(f"USD: {current_usd_rate} (current) vs {expected_usd_rate:.9f} (expected)")
    print(f"AED: {current_aed_rate} (current) vs {expected_aed_rate:.9f} (expected)")
    print()
    
    # Calculate the error factor
    usd_factor = expected_usd_rate / current_usd_rate
    aed_factor = expected_aed_rate / current_aed_rate
    
    print("Error factors:")
    print(f"USD factor: {usd_factor:.1f}x too small")
    print(f"AED factor: {aed_factor:.1f}x too small")
    print()
    
    # Let's trace the calculation
    print("=== TRACING THE CALCULATION ===")
    
    # API data
    api_rates = {
        'INR': 85.94,
        'AED': 3.67
    }
    
    company_to_usd_rate = api_rates['INR']  # 85.94
    
    print(f"API: 1 USD = {company_to_usd_rate} INR")
    print(f"API: 1 USD = {api_rates['AED']} AED")
    print()
    
    # My calculation for USD
    print("USD calculation:")
    print("My code: odoo_rate = 1.0 / company_to_usd_rate")
    my_usd_calc = 1.0 / company_to_usd_rate
    print(f"Result: 1.0 / {company_to_usd_rate} = {my_usd_calc:.9f}")
    print(f"Expected: {expected_usd_rate:.9f}")
    print(f"Match: {abs(my_usd_calc - expected_usd_rate) < 0.000001}")
    print()
    
    # My calculation for AED
    print("AED calculation:")
    print("My code: odoo_rate = api_rates['AED'] / company_to_usd_rate")
    my_aed_calc = api_rates['AED'] / company_to_usd_rate
    print(f"Result: {api_rates['AED']} / {company_to_usd_rate} = {my_aed_calc:.9f}")
    print(f"Expected: {expected_aed_rate:.9f}")
    print(f"Match: {abs(my_aed_calc - expected_aed_rate) < 0.000001}")
    print()
    
    print("=== CONCLUSION ===")
    if abs(my_usd_calc - expected_usd_rate) < 0.000001:
        print("✅ My calculation logic is CORRECT")
        print("❌ The problem must be in the API data or execution")
        print()
        print("Possible issues:")
        print("1. API returned different rates than expected")
        print("2. company_to_usd_rate is wrong")
        print("3. Code execution error")
        
        # Check what could cause 60x error
        print()
        print("To get 60x smaller rates:")
        print(f"If company_to_usd_rate was {company_to_usd_rate * 60} instead of {company_to_usd_rate}")
        print(f"Then USD rate would be: 1/{company_to_usd_rate * 60} = {1/(company_to_usd_rate * 60):.6f}")
        print("This matches the wrong result!")
        
    else:
        print("❌ My calculation logic is WRONG")
        print("Need to fix the formula")

if __name__ == "__main__":
    debug_rate_calculation()
