<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- DISABLED - Test page for popups -->
    <!--
    <template id="test_popups_page" name="Test Popups Page">
        <t t-call="website.layout">
            <div id="wrap">
                <div class="container mt-5">
                    <div class="row">
                        <div class="col-12">
                            <h1 class="text-center mb-4">Test Popups - Amigo Attires</h1>
                            <p class="text-center text-muted">Test the country selection and size chart popups</p>
                            
                            <div class="row mt-5">
                                <!-- Country Selection Test -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fa fa-globe"></i> Country Selection</h5>
                                        </div>
                                        <div class="card-body">
                                            <p>Test the country selection popup for currency conversion.</p>
                                            <button class="btn btn-primary" onclick="CountrySelection.openModal()">
                                                <i class="fa fa-globe"></i> Open Country Selection
                                            </button>
                                            <hr/>
                                            <button class="btn btn-outline-secondary btn-sm" onclick="CountrySelection.resetSelection()">
                                                Reset Selection
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Size Chart Test -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fa fa-ruler"></i> Size Chart</h5>
                                        </div>
                                        <div class="card-body">
                                            <p>Test the size chart popup with demo data.</p>
                                            <button class="btn btn-info" onclick="openDemoSizeChart('test')">
                                                <i class="fa fa-ruler"></i> Open Size Chart
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Demo Product Cards -->
                            <div class="row mt-5">
                                <div class="col-12">
                                    <h3>Demo Products with Size Charts</h3>
                                    <p class="text-muted">These are sample products to test size chart functionality</p>
                                </div>
                                
                                <!-- Men's Kurta -->
                                <div class="col-md-4 mb-4">
                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title">Men's Cotton Kurta</h5>
                                            <p class="card-text">Traditional Indian kurta for men</p>
                                            <p class="text-success font-weight-bold">₹1,999</p>
                                            <button class="btn btn-outline-info btn-sm" onclick="openDemoSizeChart('men_kurta')">
                                                <i class="fa fa-ruler"></i> Size Chart
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Women's Kurta -->
                                <div class="col-md-4 mb-4">
                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title">Women's Embroidered Kurta</h5>
                                            <p class="card-text">Elegant kurta with beautiful embroidery</p>
                                            <p class="text-success font-weight-bold">₹2,499</p>
                                            <button class="btn btn-outline-info btn-sm" onclick="openDemoSizeChart('women_kurta')">
                                                <i class="fa fa-ruler"></i> Size Chart
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Women's Anarkali -->
                                <div class="col-md-4 mb-4">
                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title">Women's Anarkali Suit</h5>
                                            <p class="card-text">Graceful anarkali for special occasions</p>
                                            <p class="text-success font-weight-bold">₹3,999</p>
                                            <button class="btn btn-outline-info btn-sm" onclick="openDemoSizeChart('women_anarkali')">
                                                <i class="fa fa-ruler"></i> Size Chart
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Demo Size Chart Modal -->
            <div class="modal fade" id="testSizeChartModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fa fa-ruler"></i> <span id="chartTitle">Size Chart</span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <!-- Unit Toggle -->
                            <div class="unit-toggle text-center mb-4">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary active" data-unit="cm">
                                        <i class="fa fa-arrows-h unit-icon"></i> cm
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" data-unit="inch">
                                        <i class="fa fa-arrows-h unit-icon"></i> inch
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Size Chart Content -->
                            <div id="sizeChartContent">
                                <!-- Content will be loaded dynamically -->
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <script>
                // Wait for DOM to be ready
                document.addEventListener('DOMContentLoaded', function() {
                    console.log('Size chart test page loaded');

                    // Initialize unit toggle functionality
                    initUnitToggle();
                });

                function openDemoSizeChart(type) {
                    console.log('Opening demo size chart for:', type);

                    // Demo size chart data
                    const sizeCharts = {
                        'test': {
                            title: 'Test Size Chart',
                            sizes: [
                                {size: 'S', number: '36', chest: 91, waist: 86, shoulder: 43, sleeve: 61, length: 102},
                                {size: 'M', number: '38', chest: 97, waist: 91, shoulder: 45, sleeve: 63, length: 104},
                                {size: 'L', number: '40', chest: 102, waist: 97, shoulder: 47, sleeve: 65, length: 106}
                            ]
                        },
                        'men_kurta': {
                            title: 'Men\'s Kurta Size Chart - Amigo Attires',
                            sizes: [
                                {size: 'S', number: '36', chest: 91, waist: 86, shoulder: 43, sleeve: 61, length: 102},
                                {size: 'M', number: '38', chest: 97, waist: 91, shoulder: 45, sleeve: 63, length: 104},
                                {size: 'L', number: '40', chest: 102, waist: 97, shoulder: 47, sleeve: 65, length: 106},
                                {size: 'XL', number: '42', chest: 107, waist: 102, shoulder: 49, sleeve: 67, length: 108},
                                {size: 'XXL', number: '44', chest: 112, waist: 107, shoulder: 51, sleeve: 69, length: 110}
                            ]
                        },
                        'women_kurta': {
                            title: 'Women\'s Kurta Size Chart - Ethnic Elegance',
                            sizes: [
                                {size: 'XS', number: '32', chest: 81, waist: 66, hip: 86, shoulder: 35, sleeve: 58, length: 102},
                                {size: 'S', number: '34', chest: 86, waist: 71, hip: 91, shoulder: 37, sleeve: 60, length: 104},
                                {size: 'M', number: '36', chest: 91, waist: 76, hip: 97, shoulder: 39, sleeve: 62, length: 106},
                                {size: 'L', number: '38', chest: 97, waist: 81, hip: 102, shoulder: 41, sleeve: 64, length: 108},
                                {size: 'XL', number: '40', chest: 102, waist: 86, hip: 107, shoulder: 43, sleeve: 66, length: 110}
                            ]
                        },
                        'women_anarkali': {
                            title: 'Women\'s Anarkali Size Chart - Ethnic Elegance',
                            sizes: [
                                {size: 'XS', number: '32', chest: 81, waist: 64, hip: 91, shoulder: 35, sleeve: 58, length: 140},
                                {size: 'S', number: '34', chest: 86, waist: 69, hip: 97, shoulder: 37, sleeve: 60, length: 142},
                                {size: 'M', number: '36', chest: 91, waist: 74, hip: 102, shoulder: 39, sleeve: 62, length: 144},
                                {size: 'L', number: '38', chest: 97, waist: 79, hip: 107, shoulder: 41, sleeve: 64, length: 146},
                                {size: 'XL', number: '40', chest: 102, waist: 84, hip: 112, shoulder: 43, sleeve: 66, length: 148}
                            ]
                        }
                    };

                    const chart = sizeCharts[type];
                    if (!chart) {
                        console.error('Chart not found for type:', type);
                        return;
                    }

                    // Update modal title
                    const titleElement = document.getElementById('chartTitle');
                    if (titleElement) {
                        titleElement.textContent = chart.title;
                    }

                    // Generate table HTML
                    let html = '<div class="table-responsive"><table class="table table-bordered size-chart-table">';
                    html += '<thead class="thead-light"><tr>';
                    html += '<th>Size</th><th>Size Number</th><th>Chest</th><th>Waist</th>';
                    if (type.includes('women')) html += '<th>Hip</th>';
                    html += '<th>Shoulder</th><th>Sleeve</th><th>Length</th>';
                    html += '</tr></thead><tbody>';

                    chart.sizes.forEach(size => {
                        html += '<tr>';
                        html += '<td class="font-weight-bold">' + size.size + '</td>';
                        html += '<td>' + size.number + '</td>';
                        html += '<td class="measurement-value" data-cm="' + size.chest + '" data-inch="' + (size.chest/2.54).toFixed(1) + '">';
                        html += '<span class="cm-value">' + size.chest + '</span><span class="inch-value d-none">' + (size.chest/2.54).toFixed(1) + '</span></td>';
                        html += '<td class="measurement-value" data-cm="' + size.waist + '" data-inch="' + (size.waist/2.54).toFixed(1) + '">';
                        html += '<span class="cm-value">' + size.waist + '</span><span class="inch-value d-none">' + (size.waist/2.54).toFixed(1) + '</span></td>';
                        if (type.includes('women')) {
                            html += '<td class="measurement-value" data-cm="' + size.hip + '" data-inch="' + (size.hip/2.54).toFixed(1) + '">';
                            html += '<span class="cm-value">' + size.hip + '</span><span class="inch-value d-none">' + (size.hip/2.54).toFixed(1) + '</span></td>';
                        }
                        html += '<td class="measurement-value" data-cm="' + size.shoulder + '" data-inch="' + (size.shoulder/2.54).toFixed(1) + '">';
                        html += '<span class="cm-value">' + size.shoulder + '</span><span class="inch-value d-none">' + (size.shoulder/2.54).toFixed(1) + '</span></td>';
                        html += '<td class="measurement-value" data-cm="' + size.sleeve + '" data-inch="' + (size.sleeve/2.54).toFixed(1) + '">';
                        html += '<span class="cm-value">' + size.sleeve + '</span><span class="inch-value d-none">' + (size.sleeve/2.54).toFixed(1) + '</span></td>';
                        html += '<td class="measurement-value" data-cm="' + size.length + '" data-inch="' + (size.length/2.54).toFixed(1) + '">';
                        html += '<span class="cm-value">' + size.length + '</span><span class="inch-value d-none">' + (size.length/2.54).toFixed(1) + '</span></td>';
                        html += '</tr>';
                    });

                    html += '</tbody></table></div>';

                    // Update modal content
                    const contentElement = document.getElementById('sizeChartContent');
                    if (contentElement) {
                        contentElement.innerHTML = html;
                    }

                    // Show modal - try both Bootstrap 4 and 5 methods
                    const modal = document.getElementById('testSizeChartModal');
                    if (modal) {
                        // Try Bootstrap 5 first
                        if (typeof bootstrap !== 'undefined' &amp;&amp; bootstrap.Modal) {
                            const bsModal = new bootstrap.Modal(modal);
                            bsModal.show();
                        }
                        // Fallback to Bootstrap 4 with jQuery
                        else if (typeof $ !== 'undefined' &amp;&amp; $.fn.modal) {
                            $(modal).modal('show');
                        }
                        // Vanilla JS fallback
                        else {
                            modal.style.display = 'block';
                            modal.classList.add('show');
                            document.body.classList.add('modal-open');
                        }
                    }
                }

                function initUnitToggle() {
                    // Unit toggle functionality with vanilla JS
                    document.addEventListener('click', function(e) {
                        if (e.target.closest('.unit-toggle .btn')) {
                            const button = e.target.closest('.unit-toggle .btn');
                            const unit = button.getAttribute('data-unit');

                            console.log('Unit toggle clicked:', unit);

                            // Update button states
                            const allButtons = document.querySelectorAll('.unit-toggle .btn');
                            allButtons.forEach(btn => btn.classList.remove('active'));
                            button.classList.add('active');

                            // Toggle measurements
                            const cmValues = document.querySelectorAll('.cm-value');
                            const inchValues = document.querySelectorAll('.inch-value');

                            if (unit === 'cm') {
                                cmValues.forEach(el => el.classList.remove('d-none'));
                                inchValues.forEach(el => el.classList.add('d-none'));
                            } else {
                                cmValues.forEach(el => el.classList.add('d-none'));
                                inchValues.forEach(el => el.classList.remove('d-none'));
                            }
                        }
                    });

                    // Close modal functionality
                    document.addEventListener('click', function(e) {
                        if (e.target.matches('[data-bs-dismiss="modal"], [data-dismiss="modal"]') ||
                            e.target.closest('[data-bs-dismiss="modal"], [data-dismiss="modal"]')) {

                            console.log('Close button clicked');

                            const modal = document.getElementById('testSizeChartModal');
                            if (modal) {
                                // Try Bootstrap 5 first
                                if (typeof bootstrap !== 'undefined' &amp;&amp; bootstrap.Modal) {
                                    const bsModal = bootstrap.Modal.getInstance(modal);
                                    if (bsModal) {
                                        bsModal.hide();
                                    }
                                }
                                // Fallback to Bootstrap 4 with jQuery
                                else if (typeof $ !== 'undefined' &amp;&amp; $.fn.modal) {
                                    $(modal).modal('hide');
                                }
                                // Vanilla JS fallback
                                else {
                                    modal.style.display = 'none';
                                    modal.classList.remove('show');
                                    document.body.classList.remove('modal-open');
                                }
                            }
                        }
                    });
                }
            </script>
        </t>
    </template>
    
    <!-- Add route for test page -->
    <record id="test_popups_page_menu" model="website.menu">
        <field name="name">Test Popups</field>
        <field name="url">/test/popups</field>
        <field name="parent_id" ref="website.main_menu"/>
        <field name="sequence">99</field>
    </record>
    -->
</odoo>
