/**
 * Simple Country Pricing JavaScript for Odoo 18 CE
 *
 * This is a simplified version of the country pricing functionality that:
 * - Doesn't rely on complex Odoo module dependencies
 * - Uses vanilla JavaScript with jQuery for DOM manipulation
 * - Provides fallback functionality for environments where the main widget fails
 * - Handles country selection, currency switching, and price updates
 *
 * This version is designed to be more compatible across different Odoo configurations
 * and serves as a backup implementation.
 */

(function() {
    'use strict';

    /**
     * Initialize country pricing functionality when DOM is ready
     * This ensures all HTML elements are available before setting up event listeners
     */
    document.addEventListener('DOMContentLoaded', function() {
        initCountryPricing();
    });

    /**
     * Main initialization function for country pricing
     *
     * Sets up the country selection modal and its associated functionality:
     * 1. Finds the country selection modal element
     * 2. Sets up event listeners for user interactions
     * 3. Checks if modal should be auto-displayed
     */
    function initCountryPricing() {
        console.log('Initializing country pricing...');

        // Locate the country selection modal in the DOM
        const modal = document.getElementById('countrySelectionModal');
        if (!modal) {
            console.log('Country selection modal not found');
            return;
        }

        // Set up all event listeners for modal interactions
        setupEventListeners(modal);

        // Check if modal should be automatically shown to user
        checkAutoShow();
    }

    /**
     * Set up event listeners for country selection modal
     *
     * Configures all user interaction handlers:
     * 1. Modal show event - triggers country loading
     * 2. Country option clicks - handles country selection
     * 3. Continue without selection - allows users to skip
     *
     * @param {HTMLElement} modal - The country selection modal element
     */
    function setupEventListeners(modal) {
        // Handle modal show event - load countries when modal opens
        $(modal).on('show.bs.modal', function() {
            console.log('Country modal opening...');
            loadCountries();
        });

        // Handle country option clicks - process user's country selection
        $(modal).on('click', '.country-option', function(e) {
            e.preventDefault();
            const countryId = $(this).data('country-id');
            selectCountry(countryId);
        });

        // Handle "continue without selection" button - let users skip
        $(modal).on('click', '#continueWithoutSelection', function(e) {
            e.preventDefault();
            // Remember that user chose to skip selection
            localStorage.setItem('country_popup_shown', 'true');
            // Close the modal
            $(modal).modal('hide');
        });
    }

    function checkAutoShow() {
        // Show country selection on first visit
        if (!localStorage.getItem('country_selection_shown')) {
            setTimeout(function() {
                $('#countrySelectionModal').modal('show');
            }, 1000);
        }
    }

    function loadCountries() {
        const countryGrid = document.getElementById('countryGrid');
        if (!countryGrid) return;

        // Show loading state
        countryGrid.innerHTML = '<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading countries...</div>';

        // Make AJAX request
        fetch('/shop/get_countries', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'call',
                params: {},
                id: 1
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Countries response:', data);
            if (data.result && data.result.success && data.result.countries) {
                renderCountries(data.result.countries);
            } else {
                showError('Failed to load countries: ' + (data.result?.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error loading countries:', error);
            showError('Failed to load countries. Please refresh the page.');
        });
    }

    function renderCountries(countries) {
        const countryGrid = document.getElementById('countryGrid');
        if (!countryGrid) return;

        let html = '';
        countries.forEach(function(country) {
            html += `
                <div class="country-option" data-country-id="${country.id}">
                    <div class="country-card">
                        <div class="country-flag-placeholder mb-2">
                            <i class="fa fa-globe"></i>
                        </div>
                        <div class="country-name">${country.name}</div>
                        <div class="country-currency text-muted">
                            ${country.currency}${country.currency_symbol ? ' (' + country.currency_symbol + ')' : ''}
                        </div>
                    </div>
                </div>
            `;
        });

        countryGrid.innerHTML = html;
    }

    function selectCountry(countryId) {
        console.log('Selecting country:', countryId);

        // Highlight selected country
        $('.country-option').removeClass('selected');
        $(`.country-option[data-country-id="${countryId}"]`).addClass('selected');

        // Make AJAX request to set country
        fetch('/shop/set_country', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'call',
                params: {
                    country_id: countryId
                },
                id: 1
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Set country response:', data);
            if (data.result && data.result.success) {
                // Store in localStorage to prevent showing popup again
                localStorage.setItem('country_selection_shown', 'true');
                
                // Close modal and reload page
                $('#countrySelectionModal').modal('hide');
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            } else {
                showError('Failed to set country: ' + (data.result?.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error setting country:', error);
            showError('Failed to set country. Please try again.');
        });
    }

    function showError(message) {
        const countryGrid = document.getElementById('countryGrid');
        if (countryGrid) {
            countryGrid.innerHTML = `<div class="text-center text-danger">${message}</div>`;
        }
    }

    // Make functions available globally for debugging
    window.countryPricing = {
        loadCountries: loadCountries,
        selectCountry: selectCountry,
        checkAutoShow: checkAutoShow
    };

})();
