// Simple Country Pricing JavaScript for Odoo 18 CE
// This version doesn't rely on complex module dependencies

(function() {
    'use strict';

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        initCountryPricing();
    });

    function initCountryPricing() {
        console.log('Initializing country pricing...');

        // Find the country selection modal
        const modal = document.getElementById('countrySelectionModal');
        if (!modal) {
            console.log('Country selection modal not found');
            return;
        }

        // Set up event listeners
        setupEventListeners(modal);

        // Check if we should auto-show the modal
        checkAutoShow();
    }

    function setupEventListeners(modal) {
        // Modal show event
        $(modal).on('show.bs.modal', function() {
            console.log('Country modal opening...');
            loadCountries();
        });

        // Country option clicks
        $(modal).on('click', '.country-option', function(e) {
            e.preventDefault();
            const countryId = $(this).data('country-id');
            selectCountry(countryId);
        });

        // Continue without selection
        $(modal).on('click', '#continueWithoutSelection', function(e) {
            e.preventDefault();
            localStorage.setItem('country_popup_shown', 'true');
            $(modal).modal('hide');
        });
    }

    function checkAutoShow() {
        // Show country selection on first visit
        if (!localStorage.getItem('country_selection_shown')) {
            setTimeout(function() {
                $('#countrySelectionModal').modal('show');
            }, 1000);
        }
    }

    function loadCountries() {
        const countryGrid = document.getElementById('countryGrid');
        if (!countryGrid) return;

        // Show loading state
        countryGrid.innerHTML = '<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading countries...</div>';

        // Make AJAX request
        fetch('/shop/get_countries', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'call',
                params: {},
                id: 1
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Countries response:', data);
            if (data.result && data.result.success && data.result.countries) {
                renderCountries(data.result.countries);
            } else {
                showError('Failed to load countries: ' + (data.result?.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error loading countries:', error);
            showError('Failed to load countries. Please refresh the page.');
        });
    }

    function renderCountries(countries) {
        const countryGrid = document.getElementById('countryGrid');
        if (!countryGrid) return;

        let html = '';
        countries.forEach(function(country) {
            html += `
                <div class="country-option" data-country-id="${country.id}">
                    <div class="country-card">
                        <div class="country-flag-placeholder mb-2">
                            <i class="fa fa-globe"></i>
                        </div>
                        <div class="country-name">${country.name}</div>
                        <div class="country-currency text-muted">
                            ${country.currency}${country.currency_symbol ? ' (' + country.currency_symbol + ')' : ''}
                        </div>
                    </div>
                </div>
            `;
        });

        countryGrid.innerHTML = html;
    }

    function selectCountry(countryId) {
        console.log('Selecting country:', countryId);

        // Highlight selected country
        $('.country-option').removeClass('selected');
        $(`.country-option[data-country-id="${countryId}"]`).addClass('selected');

        // Make AJAX request to set country
        fetch('/shop/set_country', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'call',
                params: {
                    country_id: countryId
                },
                id: 1
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Set country response:', data);
            if (data.result && data.result.success) {
                // Store in localStorage to prevent showing popup again
                localStorage.setItem('country_selection_shown', 'true');
                
                // Close modal and reload page
                $('#countrySelectionModal').modal('hide');
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            } else {
                showError('Failed to set country: ' + (data.result?.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error setting country:', error);
            showError('Failed to set country. Please try again.');
        });
    }

    function showError(message) {
        const countryGrid = document.getElementById('countryGrid');
        if (countryGrid) {
            countryGrid.innerHTML = `<div class="text-center text-danger">${message}</div>`;
        }
    }

    // Make functions available globally for debugging
    window.countryPricing = {
        loadCountries: loadCountries,
        selectCountry: selectCountry,
        checkAutoShow: checkAutoShow
    };

})();
